import 'package:flutter/material.dart';

/// إعدادات الخطوط المستخدمة في التطبيق
class FontConfig {
  // أسماء الخطوط
  static const String quranFontFamily = 'UthmanicQaloon';
  static const String uiFontFamily = 'Default'; // الخط الافتراضي للنظام
  
  // أحجام الخطوط الافتراضية
  static const double defaultQuranFontSize = 20.0;
  static const double defaultUIFontSize = 16.0;
  static const double defaultHeaderFontSize = 24.0;
  static const double defaultVerseNumberFontSize = 18.0;
  
  // حدود أحجام الخطوط
  static const double minFontSize = 12.0;
  static const double maxFontSize = 36.0;
  
  // تباعد الأسطر
  static const double defaultLineSpacing = 1.8;
  static const double minLineSpacing = 1.0;
  static const double maxLineSpacing = 3.0;
  
  /// الحصول على نمط النص للقرآن الكريم
  static TextStyle getQuranTextStyle({
    required BuildContext context,
    double? fontSize,
    double? lineSpacing,
    Color? color,
    FontWeight? fontWeight,
  }) {
    final theme = Theme.of(context);
    return TextStyle(
      fontFamily: quranFontFamily,
      fontSize: fontSize ?? defaultQuranFontSize,
      height: lineSpacing ?? defaultLineSpacing,
      color: color ?? theme.textTheme.bodyLarge?.color,
      fontWeight: fontWeight ?? FontWeight.w400,
      textBaseline: TextBaseline.alphabetic,
    );
  }
  
  /// الحصول على نمط النص لرؤوس السور
  static TextStyle getSurahHeaderTextStyle({
    required BuildContext context,
    double? fontSize,
    double? lineSpacing,
    Color? color,
  }) {
    final theme = Theme.of(context);
    return TextStyle(
      fontFamily: quranFontFamily,
      fontSize: fontSize ?? defaultHeaderFontSize,
      height: lineSpacing ?? defaultLineSpacing,
      color: color ?? theme.primaryColor,
      fontWeight: FontWeight.bold,
      textBaseline: TextBaseline.alphabetic,
    );
  }
  
  /// الحصول على نمط النص لأرقام الآيات
  static TextStyle getVerseNumberTextStyle({
    required BuildContext context,
    double? fontSize,
    Color? color,
  }) {
    final theme = Theme.of(context);
    return TextStyle(
      fontFamily: quranFontFamily,
      fontSize: fontSize ?? defaultVerseNumberFontSize,
      color: color ?? theme.primaryColor,
      fontWeight: FontWeight.w600,
      textBaseline: TextBaseline.alphabetic,
    );
  }
  
  /// الحصول على نمط النص للواجهة
  static TextStyle getUITextStyle({
    required BuildContext context,
    double? fontSize,
    Color? color,
    FontWeight? fontWeight,
  }) {
    final theme = Theme.of(context);
    return TextStyle(
      fontSize: fontSize ?? defaultUIFontSize,
      color: color ?? theme.textTheme.bodyLarge?.color,
      fontWeight: fontWeight ?? FontWeight.normal,
    );
  }
  
  /// الحصول على نمط النص للعناوين
  static TextStyle getHeadingTextStyle({
    required BuildContext context,
    double? fontSize,
    Color? color,
    FontWeight? fontWeight,
  }) {
    final theme = Theme.of(context);
    return TextStyle(
      fontSize: fontSize ?? (defaultUIFontSize + 4),
      color: color ?? theme.textTheme.headlineSmall?.color,
      fontWeight: fontWeight ?? FontWeight.w600,
    );
  }
  
  /// التحقق من صحة حجم الخط
  static bool isValidFontSize(double fontSize) {
    return fontSize >= minFontSize && fontSize <= maxFontSize;
  }
  
  /// التحقق من صحة تباعد الأسطر
  static bool isValidLineSpacing(double lineSpacing) {
    return lineSpacing >= minLineSpacing && lineSpacing <= maxLineSpacing;
  }
  
  /// تطبيق حجم الخط المناسب
  static double clampFontSize(double fontSize) {
    return fontSize.clamp(minFontSize, maxFontSize);
  }
  
  /// تطبيق تباعد الأسطر المناسب
  static double clampLineSpacing(double lineSpacing) {
    return lineSpacing.clamp(minLineSpacing, maxLineSpacing);
  }
  
  /// الحصول على معلومات الخط المستخدم
  static Map<String, dynamic> getFontInfo() {
    return {
      'quranFont': {
        'family': quranFontFamily,
        'description': 'خط عثماني قالون الإصدار الخامس',
        'usage': 'النصوص القرآنية ورؤوس السور وأرقام الآيات',
        'features': [
          'مصمم خصيصاً لرواية قالون',
          'يدعم جميع علامات التشكيل',
          'مقروء وجميل للنصوص الطويلة',
          'متوافق مع معايير الطباعة العربية',
        ],
      },
      'uiFont': {
        'family': 'النظام الافتراضي',
        'description': 'خط النظام الافتراضي',
        'usage': 'واجهة المستخدم والنصوص العادية',
      },
    };
  }
  
  /// الحصول على أحجام الخطوط المقترحة
  static List<double> getSuggestedFontSizes() {
    return [
      12.0, 14.0, 16.0, 18.0, 20.0, 22.0, 24.0, 26.0, 28.0, 30.0, 32.0, 36.0
    ];
  }
  
  /// الحصول على قيم تباعد الأسطر المقترحة
  static List<double> getSuggestedLineSpacings() {
    return [
      1.0, 1.2, 1.4, 1.5, 1.6, 1.8, 2.0, 2.2, 2.5, 2.8, 3.0
    ];
  }
  
  /// تحويل حجم الخط إلى وصف نصي
  static String getFontSizeDescription(double fontSize) {
    if (fontSize <= 14) return 'صغير جداً';
    if (fontSize <= 16) return 'صغير';
    if (fontSize <= 18) return 'متوسط';
    if (fontSize <= 22) return 'كبير';
    if (fontSize <= 26) return 'كبير جداً';
    return 'ضخم';
  }
  
  /// تحويل تباعد الأسطر إلى وصف نصي
  static String getLineSpacingDescription(double lineSpacing) {
    if (lineSpacing <= 1.2) return 'ضيق';
    if (lineSpacing <= 1.5) return 'متوسط';
    if (lineSpacing <= 2.0) return 'واسع';
    return 'واسع جداً';
  }
}
