# future
Y+1={0} বছরে
M+1={0} মাসে
W+1={0} সপ্তাহে
D+1={0} দিনের মধ্যে
H+1={0} ঘন্টায়
N+1={0} মিনিটে
S+1={0} সেকেন্ডে

Y+5={0} বছরে
M+5={0} মাসে
W+5={0} সপ্তাহে
D+5={0} দিনের মধ্যে
H+5={0} ঘন্টায়
N+5={0} মিনিটে
S+5={0} সেকেন্ডে

# past
Y-1={0} বছর পূর্বে
M-1={0} মাস আগে
W-1={0} সপ্তাহ আগে
D-1={0} দিন আগে
H-1={0} ঘন্টা আগে
N-1={0} মিনিট আগে
S-1={0} সেকেন্ড পূর্বে

Y-5={0} বছর পূর্বে
M-5={0} মাস আগে
W-5={0} সপ্তাহ আগে
D-5={0} দিন আগে
H-5={0} ঘন্টা আগে
N-5={0} মিনিট আগে
S-5={0} সেকেন্ড পূর্বে

# current time
now=এখন

# future (short)
y+1={0} বছরে
m+1={0} মাসে
w+1={0} সপ্তাহে
d+1={0} দিনের মধ্যে
h+1={0} ঘন্টায়
n+1={0} মিনিটে
s+1={0} সেকেন্ডে

y+5={0} বছরে
m+5={0} মাসে
w+5={0} সপ্তাহে
d+5={0} দিনের মধ্যে
h+5={0} ঘন্টায়
n+5={0} মিনিটে
s+5={0} সেকেন্ডে

# past (short)
y-1={0} বছর পূর্বে
m-1={0} মাস আগে
w-1={0} সপ্তাহ আগে
d-1={0} দিন আগে
h-1={0} ঘন্টা আগে
n-1={0} মিনিট আগে
s-1={0} সেকেন্ড পূর্বে

y-5={0} বছর পূর্বে
m-5={0} মাস আগে
w-5={0} সপ্তাহ আগে
d-5={0} দিন আগে
h-5={0} ঘন্টা আগে
n-5={0} মিনিট আগে
s-5={0} সেকেন্ড পূর্বে

# relative day
yesterday=গতকাল
today=আজ
tomorrow=আগামীকাল

mon-=গত সোমবার
mon+=পরের সোমবার
tue-=গত মঙ্গলবার
tue+=পরের মঙ্গলবার
wed-=গত বুধবার
wed+=পরের বুধবার
thu-=গত বৃহস্পতিবার
thu+=পরের বৃহস্পতিবার
fri-=গত শুক্রবার
fri+=পরের শুক্রবার
sat-=গত শনিবার
sat+=পরের শনিবার
sun-=গত রবিবার
sun+=পরের রবিবার
