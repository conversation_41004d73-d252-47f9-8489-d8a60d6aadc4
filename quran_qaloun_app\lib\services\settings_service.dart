import 'package:shared_preferences/shared_preferences.dart';

class SettingsService {
  static SettingsService? _instance;
  static SettingsService get instance => _instance ??= SettingsService._();
  SettingsService._();

  // مفاتيح الإعدادات
  static const String _fontSizeKey = 'font_size';
  static const String _isDarkModeKey = 'is_dark_mode';
  static const String _lastReadPageKey = 'last_read_page';
  static const String _lastReadSurahKey = 'last_read_surah';
  static const String _lastReadVerseKey = 'last_read_verse';
  static const String _isFirstLaunchKey = 'is_first_launch';
  static const String _arabicFontFamilyKey = 'arabic_font_family';
  static const String _lineSpacingKey = 'line_spacing';
  static const String _showVerseNumbersKey = 'show_verse_numbers';
  static const String _showSurahHeadersKey = 'show_surah_headers';

  // القيم الافتراضية
  static const double _defaultFontSize = 18.0;
  static const bool _defaultIsDarkMode = false;
  static const int _defaultLastReadPage = 1;
  static const String _defaultArabicFontFamily = 'Default';
  static const double _defaultLineSpacing = 1.5;
  static const bool _defaultShowVerseNumbers = true;
  static const bool _defaultShowSurahHeaders = true;

  /// حجم الخط
  Future<double> getFontSize() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getDouble(_fontSizeKey) ?? _defaultFontSize;
  }

  Future<bool> setFontSize(double fontSize) async {
    final prefs = await SharedPreferences.getInstance();
    return await prefs.setDouble(_fontSizeKey, fontSize);
  }

  /// النمط الليلي
  Future<bool> getIsDarkMode() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isDarkModeKey) ?? _defaultIsDarkMode;
  }

  Future<bool> setIsDarkMode(bool isDarkMode) async {
    final prefs = await SharedPreferences.getInstance();
    return await prefs.setBool(_isDarkModeKey, isDarkMode);
  }

  /// آخر صفحة مقروءة
  Future<int> getLastReadPage() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_lastReadPageKey) ?? _defaultLastReadPage;
  }

  Future<bool> setLastReadPage(int page) async {
    final prefs = await SharedPreferences.getInstance();
    return await prefs.setInt(_lastReadPageKey, page);
  }

  /// آخر سورة مقروءة
  Future<String?> getLastReadSurah() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_lastReadSurahKey);
  }

  Future<bool> setLastReadSurah(String surahName) async {
    final prefs = await SharedPreferences.getInstance();
    return await prefs.setString(_lastReadSurahKey, surahName);
  }

  /// آخر آية مقروءة
  Future<int?> getLastReadVerse() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_lastReadVerseKey);
  }

  Future<bool> setLastReadVerse(int verseId) async {
    final prefs = await SharedPreferences.getInstance();
    return await prefs.setInt(_lastReadVerseKey, verseId);
  }

  /// حفظ موقع القراءة الحالي
  Future<bool> saveReadingPosition(int page, String surahName, int verseId) async {
    final results = await Future.wait([
      setLastReadPage(page),
      setLastReadSurah(surahName),
      setLastReadVerse(verseId),
    ]);
    return results.every((result) => result);
  }

  /// التشغيل الأول
  Future<bool> getIsFirstLaunch() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isFirstLaunchKey) ?? true;
  }

  Future<bool> setIsFirstLaunch(bool isFirstLaunch) async {
    final prefs = await SharedPreferences.getInstance();
    return await prefs.setBool(_isFirstLaunchKey, isFirstLaunch);
  }

  /// خط النص العربي
  Future<String> getArabicFontFamily() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_arabicFontFamilyKey) ?? _defaultArabicFontFamily;
  }

  Future<bool> setArabicFontFamily(String fontFamily) async {
    final prefs = await SharedPreferences.getInstance();
    return await prefs.setString(_arabicFontFamilyKey, fontFamily);
  }

  /// تباعد الأسطر
  Future<double> getLineSpacing() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getDouble(_lineSpacingKey) ?? _defaultLineSpacing;
  }

  Future<bool> setLineSpacing(double lineSpacing) async {
    final prefs = await SharedPreferences.getInstance();
    return await prefs.setDouble(_lineSpacingKey, lineSpacing);
  }

  /// إظهار أرقام الآيات
  Future<bool> getShowVerseNumbers() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_showVerseNumbersKey) ?? _defaultShowVerseNumbers;
  }

  Future<bool> setShowVerseNumbers(bool showVerseNumbers) async {
    final prefs = await SharedPreferences.getInstance();
    return await prefs.setBool(_showVerseNumbersKey, showVerseNumbers);
  }

  /// إظهار رؤوس السور
  Future<bool> getShowSurahHeaders() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_showSurahHeadersKey) ?? _defaultShowSurahHeaders;
  }

  Future<bool> setShowSurahHeaders(bool showSurahHeaders) async {
    final prefs = await SharedPreferences.getInstance();
    return await prefs.setBool(_showSurahHeadersKey, showSurahHeaders);
  }

  /// إعادة تعيين جميع الإعدادات
  Future<bool> resetAllSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final keys = [
      _fontSizeKey,
      _isDarkModeKey,
      _lastReadPageKey,
      _lastReadSurahKey,
      _lastReadVerseKey,
      _arabicFontFamilyKey,
      _lineSpacingKey,
      _showVerseNumbersKey,
      _showSurahHeadersKey,
    ];

    final results = await Future.wait(
      keys.map((key) => prefs.remove(key))
    );

    return results.every((result) => result);
  }

  /// الحصول على جميع الإعدادات
  Future<Map<String, dynamic>> getAllSettings() async {
    return {
      'fontSize': await getFontSize(),
      'isDarkMode': await getIsDarkMode(),
      'lastReadPage': await getLastReadPage(),
      'lastReadSurah': await getLastReadSurah(),
      'lastReadVerse': await getLastReadVerse(),
      'isFirstLaunch': await getIsFirstLaunch(),
      'arabicFontFamily': await getArabicFontFamily(),
      'lineSpacing': await getLineSpacing(),
      'showVerseNumbers': await getShowVerseNumbers(),
      'showSurahHeaders': await getShowSurahHeaders(),
    };
  }
}
