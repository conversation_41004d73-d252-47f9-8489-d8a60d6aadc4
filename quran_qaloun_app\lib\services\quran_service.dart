import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/surah.dart';
import '../models/surah_info.dart';
import '../models/verse.dart';

class QuranService {
  static QuranService? _instance;
  static QuranService get instance => _instance ??= QuranService._();
  QuranService._();

  List<Surah>? _surahs;
  List<SurahInfo>? _surahInfoList;
  Map<int, List<Surah>>? _pageToSurahs;

  /// تحميل جميع السور من ملف JSON
  Future<List<Surah>> loadSurahs() async {
    if (_surahs != null) return _surahs!;

    try {
      final String jsonString = await rootBundle.loadString('assets/quran.qaloun.json');
      final List<dynamic> jsonList = json.decode(jsonString);
      
      _surahs = jsonList.map((json) => Surah.fromJson(json)).toList();
      _buildPageIndex();
      
      return _surahs!;
    } catch (e) {
      throw Exception('خطأ في تحميل بيانات القرآن: $e');
    }
  }

  /// تحميل قائمة معلومات السور
  Future<List<SurahInfo>> loadSurahInfoList() async {
    if (_surahInfoList != null) return _surahInfoList!;

    try {
      final String jsonString = await rootBundle.loadString('assets/surah.list.qaloun.json');
      final List<dynamic> jsonList = json.decode(jsonString);
      
      _surahInfoList = jsonList.map((json) => SurahInfo.fromJson(json)).toList();
      
      return _surahInfoList!;
    } catch (e) {
      throw Exception('خطأ في تحميل قائمة السور: $e');
    }
  }

  /// بناء فهرس الصفحات
  void _buildPageIndex() {
    if (_surahs == null) return;

    _pageToSurahs = {};
    for (final surah in _surahs!) {
      if (_pageToSurahs![surah.page] == null) {
        _pageToSurahs![surah.page] = [];
      }
      _pageToSurahs![surah.page]!.add(surah);
    }
  }

  /// الحصول على السور في صفحة معينة
  List<Surah> getSurahsInPage(int page) {
    if (_pageToSurahs == null) return [];
    return _pageToSurahs![page] ?? [];
  }

  /// البحث في القرآن
  Future<List<SearchResult>> searchInQuran(String query) async {
    if (query.trim().isEmpty) return [];
    
    final surahs = await loadSurahs();
    final results = <SearchResult>[];
    
    final searchQuery = query.trim().toLowerCase();
    
    for (final surah in surahs) {
      for (final verse in surah.verses) {
        if (verse.text.toLowerCase().contains(searchQuery)) {
          results.add(SearchResult(
            surah: surah,
            verse: verse,
            highlightedText: _highlightText(verse.text, query),
          ));
        }
      }
    }
    
    return results;
  }

  /// تمييز النص المطابق في نتائج البحث
  String _highlightText(String text, String query) {
    if (query.trim().isEmpty) return text;
    
    final regex = RegExp(RegExp.escape(query.trim()), caseSensitive: false);
    return text.replaceAllMapped(regex, (match) => '**${match.group(0)}**');
  }

  /// الحصول على السورة بالاسم
  Future<Surah?> getSurahByName(String name) async {
    final surahs = await loadSurahs();
    try {
      return surahs.firstWhere((surah) => surah.name == name);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على معلومات السورة بالاسم
  Future<SurahInfo?> getSurahInfoByName(String name) async {
    final surahInfoList = await loadSurahInfoList();
    try {
      return surahInfoList.firstWhere((info) => info.name == name);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على إجمالي عدد الصفحات
  Future<int> getTotalPages() async {
    final surahInfoList = await loadSurahInfoList();
    if (surahInfoList.isEmpty) return 0;
    
    return surahInfoList.map((info) => info.page).reduce((a, b) => a > b ? a : b);
  }

  /// الحصول على رقم الصفحة للسورة
  Future<int?> getPageForSurah(String surahName) async {
    final surahInfo = await getSurahInfoByName(surahName);
    return surahInfo?.page;
  }
}

/// نتيجة البحث
class SearchResult {
  final Surah surah;
  final Verse verse;
  final String highlightedText;

  const SearchResult({
    required this.surah,
    required this.verse,
    required this.highlightedText,
  });

  @override
  String toString() {
    return 'SearchResult{surah: ${surah.name}, verse: ${verse.id}}';
  }
}
