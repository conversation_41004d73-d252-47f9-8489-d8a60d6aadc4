import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/quran_provider.dart';
import '../providers/bookmark_provider.dart';
import '../models/surah_info.dart';
import '../widgets/surah_header_widget.dart';

class SurahListScreen extends StatefulWidget {
  const SurahListScreen({super.key});

  @override
  State<SurahListScreen> createState() => _SurahListScreenState();
}

class _SurahListScreenState extends State<SurahListScreen> {
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<QuranProvider, BookmarkProvider>(
      builder: (context, quranProvider, bookmarkProvider, child) {
        if (quranProvider.isLoading) {
          return const Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري تحميل قائمة السور...'),
                ],
              ),
            ),
          );
        }

        if (quranProvider.errorMessage != null) {
          return Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    quranProvider.errorMessage!,
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      quranProvider.refresh();
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            ),
          );
        }

        final filteredSurahs = _getFilteredSurahs(quranProvider.surahInfoList);

        return Scaffold(
          appBar: AppBar(
            title: const Text('فهرس السور'),
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(60),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'ابحث عن سورة...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
            ),
          ),
          body: Column(
            children: [
              // إحصائيات سريعة
              _buildQuickStats(quranProvider),
              
              // قائمة السور
              Expanded(
                child: filteredSurahs.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                        padding: const EdgeInsets.all(8),
                        itemCount: filteredSurahs.length,
                        itemBuilder: (context, index) {
                          final surahInfo = filteredSurahs[index];
                          final surahNumber = quranProvider.surahInfoList.indexOf(surahInfo) + 1;
                          
                          return _buildSurahCard(
                            context,
                            surahInfo,
                            surahNumber,
                            quranProvider,
                            bookmarkProvider,
                          );
                        },
                      ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildQuickStats(QuranProvider quranProvider) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.all(8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.primaryColor.withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            context,
            Icons.menu_book,
            '${_convertToArabicNumbers(quranProvider.getTotalSurahs())}',
            'سورة',
          ),
          _buildStatItem(
            context,
            Icons.format_quote,
            '${_convertToArabicNumbers(quranProvider.getTotalVerses())}',
            'آية',
          ),
          _buildStatItem(
            context,
            Icons.pages,
            '${_convertToArabicNumbers(quranProvider.totalPages)}',
            'صفحة',
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, IconData icon, String number, String label) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        Icon(
          icon,
          color: theme.primaryColor,
          size: 24,
        ),
        const SizedBox(height: 4),
        Text(
          number,
          style: TextStyle(
            color: theme.primaryColor,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: theme.primaryColor.withOpacity(0.7),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildSurahCard(
    BuildContext context,
    SurahInfo surahInfo,
    int surahNumber,
    QuranProvider quranProvider,
    BookmarkProvider bookmarkProvider,
  ) {
    final theme = Theme.of(context);
    final bookmarksForSurah = bookmarkProvider.getBookmarksForSurah(surahInfo.name);
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: theme.primaryColor,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Center(
            child: Text(
              _convertToArabicNumbers(surahNumber),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        title: Text(
          surahInfo.name,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Row(
          children: [
            Icon(
              Icons.format_quote,
              size: 14,
              color: theme.textTheme.bodySmall?.color,
            ),
            const SizedBox(width: 4),
            Text('${_convertToArabicNumbers(surahInfo.totalVerses)} آية'),
            const SizedBox(width: 16),
            Icon(
              Icons.menu_book,
              size: 14,
              color: theme.textTheme.bodySmall?.color,
            ),
            const SizedBox(width: 4),
            Text('صفحة ${_convertToArabicNumbers(surahInfo.page)}'),
            if (bookmarksForSurah.isNotEmpty) ...[
              const SizedBox(width: 16),
              Icon(
                Icons.bookmark,
                size: 14,
                color: theme.primaryColor,
              ),
              const SizedBox(width: 4),
              Text(
                '${_convertToArabicNumbers(bookmarksForSurah.length)}',
                style: TextStyle(color: theme.primaryColor),
              ),
            ],
          ],
        ),
        trailing: const Icon(Icons.chevron_left),
        onTap: () {
          // الانتقال إلى السورة
          quranProvider.goToSurah(surahInfo.name);
          
          // العودة إلى شاشة القراءة
          DefaultTabController.of(context).animateTo(0);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لم يتم العثور على سور تطابق البحث',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب البحث بكلمات أخرى',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  List<SurahInfo> _getFilteredSurahs(List<SurahInfo> surahs) {
    if (_searchQuery.isEmpty) {
      return surahs;
    }

    final query = _searchQuery.toLowerCase().trim();
    return surahs.where((surah) {
      return surah.name.toLowerCase().contains(query);
    }).toList();
  }

  /// تحويل الأرقام الإنجليزية إلى عربية
  String _convertToArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitInt = int.tryParse(digit);
      return digitInt != null ? arabicNumbers[digitInt] : digit;
    }).join();
  }
}
