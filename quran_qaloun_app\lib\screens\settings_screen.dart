import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../providers/bookmark_provider.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<SettingsProvider, BookmarkProvider>(
      builder: (context, settingsProvider, bookmarkProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('الإعدادات'),
          ),
          body: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // معلومات التطبيق
              _buildAppInfoSection(context),
              
              const SizedBox(height: 24),
              
              // إعدادات العرض
              _buildDisplaySection(context, settingsProvider),
              
              const SizedBox(height: 24),
              
              // إعدادات القراءة
              _buildReadingSection(context, settingsProvider),
              
              const SizedBox(height: 24),
              
              // إعدادات البيانات
              _buildDataSection(context, settingsProvider, bookmarkProvider),
              
              const SizedBox(height: 24),
              
              // معلومات إضافية
              _buildAboutSection(context),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAppInfoSection(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // أيقونة التطبيق
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: theme.primaryColor,
                borderRadius: BorderRadius.circular(40),
              ),
              child: const Icon(
                Icons.menu_book,
                size: 40,
                color: Colors.white,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // اسم التطبيق
            Text(
              'القرآن الكريم',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.primaryColor,
              ),
            ),
            
            const SizedBox(height: 4),
            
            // العنوان الفرعي
            Text(
              'برواية قالون عن نافع',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.textTheme.bodyMedium?.color,
              ),
            ),
            
            const SizedBox(height: 8),
            
            // رقم الإصدار
            Text(
              'الإصدار ١.٠.٠',
              style: theme.textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDisplaySection(BuildContext context, SettingsProvider settingsProvider) {
    return _buildSection(
      context,
      'إعدادات العرض',
      Icons.display_settings,
      [
        // النمط الليلي
        SwitchListTile(
          title: const Text('النمط الليلي'),
          subtitle: const Text('تفعيل الوضع المظلم لراحة العينين'),
          value: settingsProvider.isDarkMode,
          onChanged: (value) {
            settingsProvider.setIsDarkMode(value);
          },
          secondary: Icon(
            settingsProvider.isDarkMode ? Icons.dark_mode : Icons.light_mode,
          ),
        ),
        
        const Divider(),
        
        // حجم الخط
        ListTile(
          title: const Text('حجم الخط'),
          subtitle: Text('الحجم الحالي: ${settingsProvider.fontSize.round()}'),
          leading: const Icon(Icons.text_fields),
          trailing: const Icon(Icons.chevron_left),
          onTap: () => _showFontSizeDialog(context, settingsProvider),
        ),
        
        const Divider(),
        
        // تباعد الأسطر
        ListTile(
          title: const Text('تباعد الأسطر'),
          subtitle: Text('التباعد الحالي: ${settingsProvider.lineSpacing.toStringAsFixed(1)}'),
          leading: const Icon(Icons.format_line_spacing),
          trailing: const Icon(Icons.chevron_left),
          onTap: () => _showLineSpacingDialog(context, settingsProvider),
        ),
      ],
    );
  }

  Widget _buildReadingSection(BuildContext context, SettingsProvider settingsProvider) {
    return _buildSection(
      context,
      'إعدادات القراءة',
      Icons.menu_book,
      [
        // إظهار أرقام الآيات
        SwitchListTile(
          title: const Text('إظهار أرقام الآيات'),
          subtitle: const Text('عرض رقم الآية بجانب النص'),
          value: settingsProvider.showVerseNumbers,
          onChanged: (value) {
            settingsProvider.setShowVerseNumbers(value);
          },
          secondary: const Icon(Icons.format_list_numbered),
        ),
        
        const Divider(),
        
        // إظهار رؤوس السور
        SwitchListTile(
          title: const Text('إظهار رؤوس السور'),
          subtitle: const Text('عرض اسم السورة في بداية كل سورة'),
          value: settingsProvider.showSurahHeaders,
          onChanged: (value) {
            settingsProvider.setShowSurahHeaders(value);
          },
          secondary: const Icon(Icons.title),
        ),
      ],
    );
  }

  Widget _buildDataSection(
    BuildContext context,
    SettingsProvider settingsProvider,
    BookmarkProvider bookmarkProvider,
  ) {
    return _buildSection(
      context,
      'إدارة البيانات',
      Icons.storage,
      [
        // آخر موقع قراءة
        ListTile(
          title: const Text('آخر موقع قراءة'),
          subtitle: Text(
            settingsProvider.lastReadSurah != null
                ? '${settingsProvider.lastReadSurah} - صفحة ${_convertToArabicNumbers(settingsProvider.lastReadPage)}'
                : 'لم يتم حفظ موقع قراءة',
          ),
          leading: const Icon(Icons.bookmark_outline),
          trailing: settingsProvider.lastReadSurah != null
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () => _showClearReadingPositionDialog(context, settingsProvider),
                )
              : null,
        ),
        
        const Divider(),
        
        // العلامات المرجعية
        ListTile(
          title: const Text('العلامات المرجعية'),
          subtitle: Text('${_convertToArabicNumbers(bookmarkProvider.bookmarksCount)} علامة محفوظة'),
          leading: const Icon(Icons.bookmark),
          trailing: bookmarkProvider.bookmarksCount > 0
              ? IconButton(
                  icon: const Icon(Icons.delete_sweep, color: Colors.red),
                  onPressed: () => _showClearBookmarksDialog(context, bookmarkProvider),
                )
              : null,
        ),
        
        const Divider(),
        
        // إعادة تعيين الإعدادات
        ListTile(
          title: const Text('إعادة تعيين الإعدادات'),
          subtitle: const Text('استعادة الإعدادات الافتراضية'),
          leading: const Icon(Icons.restore, color: Colors.orange),
          trailing: const Icon(Icons.chevron_left),
          onTap: () => _showResetSettingsDialog(context, settingsProvider),
        ),
      ],
    );
  }

  Widget _buildAboutSection(BuildContext context) {
    return _buildSection(
      context,
      'حول التطبيق',
      Icons.info,
      [
        ListTile(
          title: const Text('عن التطبيق'),
          subtitle: const Text('معلومات حول تطبيق القرآن الكريم'),
          leading: const Icon(Icons.info_outline),
          trailing: const Icon(Icons.chevron_left),
          onTap: () => _showAboutDialog(context),
        ),
        
        const Divider(),
        
        ListTile(
          title: const Text('الدعم والمساعدة'),
          subtitle: const Text('تواصل معنا للحصول على المساعدة'),
          leading: const Icon(Icons.help_outline),
          trailing: const Icon(Icons.chevron_left),
          onTap: () => _showSupportDialog(context),
        ),
        
        const Divider(),
        
        ListTile(
          title: const Text('تقييم التطبيق'),
          subtitle: const Text('ساعدنا بتقييم التطبيق'),
          leading: const Icon(Icons.star_outline),
          trailing: const Icon(Icons.chevron_left),
          onTap: () => _rateApp(context),
        ),
      ],
    );
  }

  Widget _buildSection(
    BuildContext context,
    String title,
    IconData icon,
    List<Widget> children,
  ) {
    final theme = Theme.of(context);
    
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(icon, color: theme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.primaryColor,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  void _showFontSizeDialog(BuildContext context, SettingsProvider settingsProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حجم الخط'),
        content: StatefulBuilder(
          builder: (context, setState) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'نموذج للنص بالحجم الحالي',
                style: TextStyle(fontSize: settingsProvider.fontSize),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  const Text('صغير'),
                  Expanded(
                    child: Slider(
                      value: settingsProvider.fontSize,
                      min: 12,
                      max: 32,
                      divisions: 20,
                      onChanged: (value) {
                        settingsProvider.setFontSize(value);
                        setState(() {});
                      },
                    ),
                  ),
                  const Text('كبير'),
                ],
              ),
              Text('${settingsProvider.fontSize.round()}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showLineSpacingDialog(BuildContext context, SettingsProvider settingsProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تباعد الأسطر'),
        content: StatefulBuilder(
          builder: (context, setState) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'نموذج للنص\nبالتباعد الحالي\nبين الأسطر',
                style: TextStyle(
                  fontSize: 16,
                  height: settingsProvider.lineSpacing,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  const Text('ضيق'),
                  Expanded(
                    child: Slider(
                      value: settingsProvider.lineSpacing,
                      min: 1.0,
                      max: 3.0,
                      divisions: 20,
                      onChanged: (value) {
                        settingsProvider.setLineSpacing(value);
                        setState(() {});
                      },
                    ),
                  ),
                  const Text('واسع'),
                ],
              ),
              Text(settingsProvider.lineSpacing.toStringAsFixed(1)),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showClearReadingPositionDialog(BuildContext context, SettingsProvider settingsProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح موقع القراءة'),
        content: const Text('هل تريد مسح آخر موقع قراءة محفوظ؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              settingsProvider.saveReadingPosition(1, '', 0);
              Navigator.pop(context);
            },
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  void _showClearBookmarksDialog(BuildContext context, BookmarkProvider bookmarkProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف جميع العلامات المرجعية'),
        content: const Text('هل أنت متأكد من حذف جميع العلامات المرجعية؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              await bookmarkProvider.clearAllBookmarks();
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف الكل'),
          ),
        ],
      ),
    );
  }

  void _showResetSettingsDialog(BuildContext context, SettingsProvider settingsProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين الإعدادات'),
        content: const Text('هل تريد استعادة جميع الإعدادات إلى القيم الافتراضية؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              await settingsProvider.resetAllSettings();
              Navigator.pop(context);
            },
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('عن التطبيق'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تطبيق القرآن الكريم برواية قالون عن نافع',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            Text('يحتوي التطبيق على:'),
            SizedBox(height: 8),
            Text('• النص الكامل للقرآن الكريم برواية قالون'),
            Text('• إمكانية البحث في القرآن'),
            Text('• العلامات المرجعية'),
            Text('• إعدادات متقدمة للقراءة'),
            Text('• واجهة عربية سهلة الاستخدام'),
            SizedBox(height: 16),
            Text(
              'تم تطوير التطبيق بعناية لخدمة كتاب الله الكريم',
              style: TextStyle(fontStyle: FontStyle.italic),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showSupportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الدعم والمساعدة'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('إذا كان لديك أي استفسار أو اقتراح، يمكنك التواصل معنا:'),
            SizedBox(height: 16),
            Text('• البريد الإلكتروني: <EMAIL>'),
            Text('• الموقع الإلكتروني: www.quranapp.com'),
            Text('• تويتر: @QuranApp'),
            SizedBox(height: 16),
            Text('نحن نقدر ملاحظاتكم ونسعى لتحسين التطبيق باستمرار.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _rateApp(BuildContext context) {
    // TODO: تنفيذ فتح متجر التطبيقات للتقييم
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('شكراً لك! سيتم فتح متجر التطبيقات قريباً'),
      ),
    );
  }

  /// تحويل الأرقام الإنجليزية إلى عربية
  String _convertToArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitInt = int.tryParse(digit);
      return digitInt != null ? arabicNumbers[digitInt] : digit;
    }).join();
  }
}
