import 'package:json_annotation/json_annotation.dart';
import 'verse.dart';

part 'surah.g.dart';

@JsonSerializable()
class Surah {
  final int jozz;
  final String name;
  final int page;
  final List<Verse> verses;

  const Surah({
    required this.jozz,
    required this.name,
    required this.page,
    required this.verses,
  });

  factory Surah.fromJson(Map<String, dynamic> json) => _$SurahFromJson(json);
  Map<String, dynamic> toJson() => _$SurahToJson(this);

  @override
  String toString() {
    return 'Surah{jozz: $jozz, name: $name, page: $page, versesCount: ${verses.length}}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Surah &&
          runtimeType == other.runtimeType &&
          jozz == other.jozz &&
          name == other.name &&
          page == other.page;

  @override
  int get hashCode => jozz.hashCode ^ name.hashCode ^ page.hashCode;
}
