import 'package:flutter/material.dart';
import '../services/settings_service.dart';

class SettingsProvider extends ChangeNotifier {
  final SettingsService _settingsService = SettingsService.instance;

  // الإعدادات الحالية
  double _fontSize = 18.0;
  bool _isDarkMode = false;
  int _lastReadPage = 1;
  String? _lastReadSurah;
  int? _lastReadVerse;
  bool _isFirstLaunch = true;
  String _arabicFontFamily = 'Default';
  double _lineSpacing = 1.5;
  bool _showVerseNumbers = true;
  bool _showSurahHeaders = true;
  bool _isLoading = false;

  // Getters
  double get fontSize => _fontSize;
  bool get isDarkMode => _isDarkMode;
  int get lastReadPage => _lastReadPage;
  String? get lastReadSurah => _lastReadSurah;
  int? get lastReadVerse => _lastReadVerse;
  bool get isFirstLaunch => _isFirstLaunch;
  String get arabicFontFamily => _arabicFontFamily;
  double get lineSpacing => _lineSpacing;
  bool get showVerseNumbers => _showVerseNumbers;
  bool get showSurahHeaders => _showSurahHeaders;
  bool get isLoading => _isLoading;

  /// تحميل الإعدادات من التخزين المحلي
  Future<void> loadSettings() async {
    _isLoading = true;
    notifyListeners();

    try {
      _fontSize = await _settingsService.getFontSize();
      _isDarkMode = await _settingsService.getIsDarkMode();
      _lastReadPage = await _settingsService.getLastReadPage();
      _lastReadSurah = await _settingsService.getLastReadSurah();
      _lastReadVerse = await _settingsService.getLastReadVerse();
      _isFirstLaunch = await _settingsService.getIsFirstLaunch();
      _arabicFontFamily = await _settingsService.getArabicFontFamily();
      _lineSpacing = await _settingsService.getLineSpacing();
      _showVerseNumbers = await _settingsService.getShowVerseNumbers();
      _showSurahHeaders = await _settingsService.getShowSurahHeaders();
    } catch (e) {
      debugPrint('خطأ في تحميل الإعدادات: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تغيير حجم الخط
  Future<void> setFontSize(double fontSize) async {
    if (_fontSize == fontSize) return;
    
    _fontSize = fontSize;
    notifyListeners();
    
    await _settingsService.setFontSize(fontSize);
  }

  /// تغيير النمط الليلي
  Future<void> setIsDarkMode(bool isDarkMode) async {
    if (_isDarkMode == isDarkMode) return;
    
    _isDarkMode = isDarkMode;
    notifyListeners();
    
    await _settingsService.setIsDarkMode(isDarkMode);
  }

  /// حفظ موقع القراءة
  Future<void> saveReadingPosition(int page, String surahName, int verseId) async {
    _lastReadPage = page;
    _lastReadSurah = surahName;
    _lastReadVerse = verseId;
    notifyListeners();
    
    await _settingsService.saveReadingPosition(page, surahName, verseId);
  }

  /// تعيين التشغيل الأول
  Future<void> setIsFirstLaunch(bool isFirstLaunch) async {
    if (_isFirstLaunch == isFirstLaunch) return;
    
    _isFirstLaunch = isFirstLaunch;
    notifyListeners();
    
    await _settingsService.setIsFirstLaunch(isFirstLaunch);
  }

  /// تغيير خط النص العربي
  Future<void> setArabicFontFamily(String fontFamily) async {
    if (_arabicFontFamily == fontFamily) return;
    
    _arabicFontFamily = fontFamily;
    notifyListeners();
    
    await _settingsService.setArabicFontFamily(fontFamily);
  }

  /// تغيير تباعد الأسطر
  Future<void> setLineSpacing(double lineSpacing) async {
    if (_lineSpacing == lineSpacing) return;
    
    _lineSpacing = lineSpacing;
    notifyListeners();
    
    await _settingsService.setLineSpacing(lineSpacing);
  }

  /// تغيير إظهار أرقام الآيات
  Future<void> setShowVerseNumbers(bool showVerseNumbers) async {
    if (_showVerseNumbers == showVerseNumbers) return;
    
    _showVerseNumbers = showVerseNumbers;
    notifyListeners();
    
    await _settingsService.setShowVerseNumbers(showVerseNumbers);
  }

  /// تغيير إظهار رؤوس السور
  Future<void> setShowSurahHeaders(bool showSurahHeaders) async {
    if (_showSurahHeaders == showSurahHeaders) return;
    
    _showSurahHeaders = showSurahHeaders;
    notifyListeners();
    
    await _settingsService.setShowSurahHeaders(showSurahHeaders);
  }

  /// إعادة تعيين جميع الإعدادات
  Future<void> resetAllSettings() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _settingsService.resetAllSettings();
      await loadSettings();
    } catch (e) {
      debugPrint('خطأ في إعادة تعيين الإعدادات: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// الحصول على نمط النص للقرآن
  TextStyle getQuranTextStyle(BuildContext context) {
    final theme = Theme.of(context);
    return TextStyle(
      fontSize: _fontSize,
      height: _lineSpacing,
      fontFamily: _arabicFontFamily == 'Default' ? null : _arabicFontFamily,
      color: theme.textTheme.bodyLarge?.color,
      fontWeight: FontWeight.w400,
    );
  }

  /// الحصول على نمط النص لرؤوس السور
  TextStyle getSurahHeaderTextStyle(BuildContext context) {
    final theme = Theme.of(context);
    return TextStyle(
      fontSize: _fontSize + 4,
      height: _lineSpacing,
      fontFamily: _arabicFontFamily == 'Default' ? null : _arabicFontFamily,
      color: theme.primaryColor,
      fontWeight: FontWeight.bold,
    );
  }

  /// الحصول على نمط النص لأرقام الآيات
  TextStyle getVerseNumberTextStyle(BuildContext context) {
    final theme = Theme.of(context);
    return TextStyle(
      fontSize: _fontSize - 2,
      fontFamily: _arabicFontFamily == 'Default' ? null : _arabicFontFamily,
      color: theme.primaryColor,
      fontWeight: FontWeight.w500,
    );
  }
}
