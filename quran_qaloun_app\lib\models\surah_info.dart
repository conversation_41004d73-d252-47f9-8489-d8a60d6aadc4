import 'package:json_annotation/json_annotation.dart';

part 'surah_info.g.dart';

@JsonSerializable()
class SurahInfo {
  final String name;
  final int page;
  final int totalVerses;

  const SurahInfo({
    required this.name,
    required this.page,
    required this.totalVerses,
  });

  factory SurahInfo.fromJson(Map<String, dynamic> json) => _$SurahInfoFromJson(json);
  Map<String, dynamic> toJson() => _$SurahInfoToJson(this);

  @override
  String toString() {
    return 'SurahInfo{name: $name, page: $page, totalVerses: $totalVerses}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SurahInfo &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          page == other.page &&
          totalVerses == other.totalVerses;

  @override
  int get hashCode => name.hashCode ^ page.hashCode ^ totalVerses.hashCode;
}
