// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Bookmark _$BookmarkFromJson(Map<String, dynamic> json) => Bookmark(
  id: json['id'] as String,
  surahName: json['surahName'] as String,
  verseId: (json['verseId'] as num).toInt(),
  page: (json['page'] as num).toInt(),
  verseText: json['verseText'] as String,
  createdAt: DateTime.parse(json['createdAt'] as String),
  note: json['note'] as String?,
);

Map<String, dynamic> _$BookmarkToJson(Bookmark instance) => <String, dynamic>{
  'id': instance.id,
  'surahName': instance.surahName,
  'verseId': instance.verseId,
  'page': instance.page,
  'verseText': instance.verseText,
  'createdAt': instance.createdAt.toIso8601String(),
  'note': instance.note,
};
