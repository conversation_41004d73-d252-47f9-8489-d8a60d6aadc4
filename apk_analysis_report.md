# تحليل تطبيق معادن ليبيا (Moaden Libya APK)

## معلومات عامة عن التطبيق

**اسم التطبيق:** معادن ليبيا (Moaden Libya)  
**اسم الحزمة:** com.mahmoud.android.MoadenLibya  
**الإصدار:** 3.0.7  
**نوع التطبيق:** تطبيق إسلامي لمواقيت الصلاة والأذكار والقرآن الكريم

## الملفات الرئيسية المستخرجة

### 1. ملف AndroidManifest.xml
- يحتوي على معلومات التطبيق الأساسية والصلاحيات
- مشفر في تنسيق ثنائي (يحتاج أدوات خاصة لقراءته)

### 2. ملفات الموارد (assets/)
التطبيق يحتوي على مجموعة غنية من الملفات:

#### أ) ملفات المدن والمواقع الجغرافية:
- **libya.cities.json**: يحتوي على 99 مدينة ليبية مع إحداثياتها الجغرافية
- **ksa.cities.json**: يحتوي على مدن المملكة العربية السعودية
- مجلدات منفصلة لكل دولة:
  - **Libya/**: ملفات JSON منفصلة لكل مدينة ليبية
  - **KSA/**: ملفات JSON منفصلة لكل مدينة سعودية

#### ب) ملفات القرآن الكريم:
- **quran.hafs.json**: القرآن الكريم برواية حفص (54,118 سطر)
- **quran.qaloun.json**: القرآن الكريم برواية قالون
- **quran.qaloun2.json**: نسخة إضافية برواية قالون
- **surah.list.hafs.json**: قائمة السور برواية حفص
- **surah.list.qaloun.json**: قائمة السور برواية قالون
- **ayah.tafseer.json**: تفسير الآيات
- **tafseer.json**: ملف التفسير الرئيسي

#### ج) ملفات الأذكار والأدعية:
مجلد **athkar/** يحتوي على:
- **morning.json**: أذكار الصباح
- **evening.json**: أذكار المساء
- **after.prayer.json**: أذكار ما بعد الصلاة
- **sleep.json**: أذكار النوم
- **after.sleep.json**: أذكار ما بعد النوم
- **duaa.json**: الأدعية
- **duaa.widget.json**: أدعية للويدجت
- **eating.json**: أذكار الطعام
- **fasting.json**: أذكار الصيام
- **home.json**: أذكار البيت
- **ruqyah.json**: الرقية الشرعية
- **salah.json**: أذكار الصلاة
- **taharah.json**: أذكار الطهارة
- **travel.json**: أذكار السفر

#### د) ملفات إضافية:
- **hadeath.json**: الأحاديث النبوية
- **help.json**: ملف المساعدة
- **whats.new.json**: ما الجديد في التطبيق
- **whats.new.ly.json**: ما الجديد (نسخة ليبيا)
- **whats.new.ksa.json**: ما الجديد (نسخة السعودية)

### 3. ملفات الكود المترجم:
- **classes.dex**: الكود الرئيسي للتطبيق
- **classes2.dex**: ملف كود إضافي
- **classes3.dex**: ملف كود إضافي

### 4. ملفات الموارد المرئية (res/):
- مئات الملفات من الصور والأيقونات والخطوط
- ملفات XML للتخطيطات والألوان
- ملفات صوتية (MP3) للأذان والتنبيهات
- خطوط عربية (TTF, OTF)

## الميزات الرئيسية للتطبيق

### 1. مواقيت الصلاة:
- يدعم 99 مدينة ليبية
- يدعم مدن المملكة العربية السعودية
- حساب دقيق لمواقيت الصلاة حسب الموقع الجغرافي

### 2. القرآن الكريم:
- نصوص كاملة برواية حفص وقالون
- تفسير الآيات
- إمكانية البحث في القرآن

### 3. الأذكار والأدعية:
- مجموعة شاملة من الأذكار اليومية
- أذكار مخصصة لأوقات مختلفة
- الرقية الشرعية

### 4. الأحاديث النبوية:
- مجموعة من الأحاديث الصحيحة

### 5. ميزات إضافية:
- تحديد اتجاه القبلة
- التقويم الإسلامي
- التنبيهات والإشعارات
- ويدجت للشاشة الرئيسية

## التقنيات المستخدمة

### المكتبات والخدمات:
- **Google Maps API**: لتحديد المواقع والقبلة
- **Firebase**: للتحليلات والإشعارات
- **Google Play Services**: للخدمات المختلفة
- **AndroidX**: مكتبات Android الحديثة
- **Room Database**: لقاعدة البيانات المحلية

### الصلاحيات المطلوبة:
- الوصول للموقع الجغرافي (GPS/Network)
- الوصول للإنترنت
- إرسال الإشعارات
- الوصول لحالة الشبكة
- الكتابة على التخزين الخارجي
- التحكم في الاهتزاز والصوت

## بنية التطبيق

### الأنشطة الرئيسية (Activities):
- **MainActivity**: الشاشة الرئيسية
- **LaunchActivity**: شاشة البداية
- **QuranActivity**: شاشة القرآن الكريم
- **QiblaJava**: شاشة تحديد القبلة
- **SettingsActivity**: شاشة الإعدادات
- **NotificationsActivity**: إعدادات الإشعارات
- **CityActivity**: اختيار المدينة

### الخدمات (Services):
- **NotificationReceiver**: استقبال الإشعارات
- **SilentReceiver**: إدارة الصمت
- **TimeChangedReceiver**: مراقبة تغيير الوقت
- **WidgetReceiver**: إدارة الويدجت

## الخلاصة

تطبيق معادن ليبيا هو تطبيق إسلامي شامل ومتطور يوفر:
- مواقيت صلاة دقيقة لليبيا والسعودية
- نصوص قرآنية كاملة بروايات متعددة
- مجموعة شاملة من الأذكار والأدعية
- واجهة مستخدم حديثة ومتقدمة
- دعم للغة العربية بشكل كامل

التطبيق مطور بعناية ويستخدم أحدث التقنيات في تطوير تطبيقات Android.
