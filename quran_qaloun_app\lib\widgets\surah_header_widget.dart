import 'package:flutter/material.dart';

class SurahHeaderWidget extends StatelessWidget {
  final String surahName;
  final int jozz;
  final int page;

  const SurahHeaderWidget({
    super.key,
    required this.surahName,
    required this.jozz,
    required this.page,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        children: [
          // خط فاصل علوي
          _buildDivider(theme),
          
          const SizedBox(height: 16),
          
          // اسم السورة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            decoration: BoxDecoration(
              color: theme.primaryColor,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: theme.primaryColor.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Text(
              'سُورَة $surahName',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              textAlign: TextAlign.center,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // معلومات إضافية (الجزء والصفحة)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildInfoChip(
                context,
                Icons.bookmark_outline,
                'الجزء ${_convertToArabicNumbers(jozz)}',
              ),
              const SizedBox(width: 12),
              _buildInfoChip(
                context,
                Icons.menu_book_outlined,
                'صفحة ${_convertToArabicNumbers(page)}',
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // خط فاصل سفلي
          _buildDivider(theme),
        ],
      ),
    );
  }

  Widget _buildDivider(ThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 1,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.transparent,
                  theme.primaryColor.withOpacity(0.3),
                  theme.primaryColor.withOpacity(0.6),
                  theme.primaryColor.withOpacity(0.3),
                  Colors.transparent,
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoChip(BuildContext context, IconData icon, String text) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: theme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: theme.primaryColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: theme.primaryColor,
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              color: theme.primaryColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// تحويل الأرقام الإنجليزية إلى عربية
  String _convertToArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitInt = int.tryParse(digit);
      return digitInt != null ? arabicNumbers[digitInt] : digit;
    }).join();
  }
}

/// ويدجت مبسط لرأس السورة
class SimpleSurahHeaderWidget extends StatelessWidget {
  final String surahName;
  final int? verseCount;

  const SimpleSurahHeaderWidget({
    super.key,
    required this.surahName,
    this.verseCount,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.primaryColor.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.menu_book,
            color: theme.primaryColor,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              surahName,
              style: TextStyle(
                color: theme.primaryColor,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          if (verseCount != null) ...[
            Text(
              '${_convertToArabicNumbers(verseCount!)} آية',
              style: TextStyle(
                color: theme.primaryColor.withOpacity(0.7),
                fontSize: 12,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// تحويل الأرقام الإنجليزية إلى عربية
  String _convertToArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitInt = int.tryParse(digit);
      return digitInt != null ? arabicNumbers[digitInt] : digit;
    }).join();
  }
}

/// ويدجت زخرفي لرأس السورة
class DecorativeSurahHeaderWidget extends StatelessWidget {
  final String surahName;
  final String? revelation; // مكية أو مدنية
  final int? verseCount;

  const DecorativeSurahHeaderWidget({
    super.key,
    required this.surahName,
    this.revelation,
    this.verseCount,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 20),
      child: Column(
        children: [
          // زخرفة علوية
          _buildDecorationLine(theme),
          
          const SizedBox(height: 16),
          
          // اسم السورة مع خلفية زخرفية
          Stack(
            alignment: Alignment.center,
            children: [
              // خلفية زخرفية
              Container(
                width: 200,
                height: 60,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      theme.primaryColor.withOpacity(0.1),
                      theme.primaryColor.withOpacity(0.2),
                      theme.primaryColor.withOpacity(0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(30),
                  border: Border.all(
                    color: theme.primaryColor.withOpacity(0.3),
                    width: 2,
                  ),
                ),
              ),
              
              // اسم السورة
              Text(
                'سُورَة $surahName',
                style: TextStyle(
                  color: theme.primaryColor,
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // معلومات إضافية
          if (revelation != null || verseCount != null)
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (revelation != null) ...[
                  _buildInfoBadge(context, revelation!),
                  if (verseCount != null) const SizedBox(width: 8),
                ],
                if (verseCount != null)
                  _buildInfoBadge(context, '${_convertToArabicNumbers(verseCount!)} آية'),
              ],
            ),
          
          const SizedBox(height: 16),
          
          // زخرفة سفلية
          _buildDecorationLine(theme),
        ],
      ),
    );
  }

  Widget _buildDecorationLine(ThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 2,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.transparent,
                  theme.primaryColor.withOpacity(0.3),
                  theme.primaryColor.withOpacity(0.6),
                  theme.primaryColor.withOpacity(0.8),
                  theme.primaryColor.withOpacity(0.6),
                  theme.primaryColor.withOpacity(0.3),
                  Colors.transparent,
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoBadge(BuildContext context, String text) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: theme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.primaryColor.withOpacity(0.3),
        ),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: theme.primaryColor,
          fontSize: 11,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// تحويل الأرقام الإنجليزية إلى عربية
  String _convertToArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitInt = int.tryParse(digit);
      return digitInt != null ? arabicNumbers[digitInt] : digit;
    }).join();
  }
}
