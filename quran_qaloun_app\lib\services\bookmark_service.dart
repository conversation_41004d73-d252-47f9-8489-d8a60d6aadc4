import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/bookmark.dart';

class BookmarkService {
  static BookmarkService? _instance;
  static BookmarkService get instance => _instance ??= BookmarkService._();
  BookmarkService._();

  static const String _bookmarksKey = 'bookmarks';

  /// حفظ علامة مرجعية جديدة
  Future<bool> saveBookmark(Bookmark bookmark) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final bookmarks = await getBookmarks();
      
      // التحقق من عدم وجود علامة مرجعية مكررة
      final existingIndex = bookmarks.indexWhere((b) => b.id == bookmark.id);
      if (existingIndex != -1) {
        bookmarks[existingIndex] = bookmark;
      } else {
        bookmarks.add(bookmark);
      }
      
      final jsonList = bookmarks.map((b) => b.toJson()).toList();
      final jsonString = json.encode(jsonList);
      
      return await prefs.setString(_bookmarksKey, jsonString);
    } catch (e) {
      return false;
    }
  }

  /// الحصول على جميع العلامات المرجعية
  Future<List<Bookmark>> getBookmarks() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_bookmarksKey);
      
      if (jsonString == null || jsonString.isEmpty) {
        return [];
      }
      
      final List<dynamic> jsonList = json.decode(jsonString);
      return jsonList.map((json) => Bookmark.fromJson(json)).toList();
    } catch (e) {
      return [];
    }
  }

  /// حذف علامة مرجعية
  Future<bool> deleteBookmark(String bookmarkId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final bookmarks = await getBookmarks();
      
      bookmarks.removeWhere((bookmark) => bookmark.id == bookmarkId);
      
      final jsonList = bookmarks.map((b) => b.toJson()).toList();
      final jsonString = json.encode(jsonList);
      
      return await prefs.setString(_bookmarksKey, jsonString);
    } catch (e) {
      return false;
    }
  }

  /// التحقق من وجود علامة مرجعية
  Future<bool> isBookmarked(String surahName, int verseId) async {
    final bookmarks = await getBookmarks();
    return bookmarks.any((bookmark) => 
        bookmark.surahName == surahName && bookmark.verseId == verseId);
  }

  /// الحصول على علامة مرجعية محددة
  Future<Bookmark?> getBookmark(String surahName, int verseId) async {
    final bookmarks = await getBookmarks();
    try {
      return bookmarks.firstWhere((bookmark) => 
          bookmark.surahName == surahName && bookmark.verseId == verseId);
    } catch (e) {
      return null;
    }
  }

  /// حذف جميع العلامات المرجعية
  Future<bool> clearAllBookmarks() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(_bookmarksKey);
    } catch (e) {
      return false;
    }
  }

  /// تحديث ملاحظة علامة مرجعية
  Future<bool> updateBookmarkNote(String bookmarkId, String note) async {
    try {
      final bookmarks = await getBookmarks();
      final index = bookmarks.indexWhere((b) => b.id == bookmarkId);
      
      if (index == -1) return false;
      
      final updatedBookmark = bookmarks[index].copyWith(note: note);
      bookmarks[index] = updatedBookmark;
      
      final prefs = await SharedPreferences.getInstance();
      final jsonList = bookmarks.map((b) => b.toJson()).toList();
      final jsonString = json.encode(jsonList);
      
      return await prefs.setString(_bookmarksKey, jsonString);
    } catch (e) {
      return false;
    }
  }

  /// البحث في العلامات المرجعية
  Future<List<Bookmark>> searchBookmarks(String query) async {
    if (query.trim().isEmpty) return await getBookmarks();
    
    final bookmarks = await getBookmarks();
    final searchQuery = query.trim().toLowerCase();
    
    return bookmarks.where((bookmark) =>
        bookmark.surahName.toLowerCase().contains(searchQuery) ||
        bookmark.verseText.toLowerCase().contains(searchQuery) ||
        (bookmark.note?.toLowerCase().contains(searchQuery) ?? false)
    ).toList();
  }

  /// ترتيب العلامات المرجعية حسب التاريخ
  Future<List<Bookmark>> getBookmarksSortedByDate({bool ascending = false}) async {
    final bookmarks = await getBookmarks();
    bookmarks.sort((a, b) => ascending 
        ? a.createdAt.compareTo(b.createdAt)
        : b.createdAt.compareTo(a.createdAt));
    return bookmarks;
  }

  /// ترتيب العلامات المرجعية حسب السورة
  Future<List<Bookmark>> getBookmarksSortedBySurah() async {
    final bookmarks = await getBookmarks();
    bookmarks.sort((a, b) {
      final surahComparison = a.surahName.compareTo(b.surahName);
      if (surahComparison != 0) return surahComparison;
      return a.verseId.compareTo(b.verseId);
    });
    return bookmarks;
  }
}
