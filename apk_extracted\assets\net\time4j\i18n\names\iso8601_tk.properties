# months
M(a)_1=ýan
M(a)_2=few
M(a)_3=mart
M(a)_4=apr
M(a)_5=maý
M(a)_6=iýun
M(a)_7=iýul
M(a)_8=awg
M(a)_9=sen
M(a)_10=okt
M(a)_11=noý
M(a)_12=dek

M(n)_1=Ý
M(n)_2=F
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=I
M(n)_7=I
M(n)_8=A
M(n)_9=S
M(n)_10=O
M(n)_11=N
M(n)_12=D

M(w)_1=ýanwar
M(w)_2=fewral
M(w)_3=mart
M(w)_4=aprel
M(w)_5=maý
M(w)_6=iýun
M(w)_7=iýul
M(w)_8=awgust
M(w)_9=sentýabr
M(w)_10=oktýabr
M(w)_11=noýabr
M(w)_12=dekabr

M(A)_1=Ýan
M(A)_2=Few
M(A)_3=Mar
M(A)_4=Apr
M(A)_5=Maý
M(A)_6=Iýun
M(A)_7=Iýul
M(A)_8=Awg
M(A)_9=Sen
M(A)_10=Okt
M(A)_11=Noý
M(A)_12=Dek

M(N)_1=Ý
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=I
M(N)_7=I
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=Ýanwar
M(W)_2=Fewral
M(W)_3=Mart
M(W)_4=Aprel
M(W)_5=Maý
M(W)_6=Iýun
M(W)_7=Iýul
M(W)_8=Awgust
M(W)_9=Sentýabr
M(W)_10=Oktýabr
M(W)_11=Noýabr
M(W)_12=Dekabr

# weekdays
D(a)_1=duş
D(a)_2=siş
D(a)_3=çar
D(a)_4=pen
D(a)_5=ann
D(a)_6=şen
D(a)_7=ýek

D(n)_1=D
D(n)_2=S
D(n)_3=Ç
D(n)_4=P
D(n)_5=A
D(n)_6=Ş
D(n)_7=Ý

D(s)_1=db
D(s)_2=sb
D(s)_3=çb
D(s)_4=pb
D(s)_5=an
D(s)_6=şb
D(s)_7=ýb

D(w)_1=duşenbe
D(w)_2=sişenbe
D(w)_3=çarşenbe
D(w)_4=penşenbe
D(w)_5=anna
D(w)_6=şenbe
D(w)_7=ýekşenbe

D(A)_1=Duş
D(A)_2=Siş
D(A)_3=Çar
D(A)_4=Pen
D(A)_5=Ann
D(A)_6=Şen
D(A)_7=Ýek

D(N)_1=D
D(N)_2=S
D(N)_3=Ç
D(N)_4=P
D(N)_5=A
D(N)_6=Ş
D(N)_7=Ý

D(S)_1=Db
D(S)_2=Sb
D(S)_3=Çb
D(S)_4=Pb
D(S)_5=An
D(S)_6=Şb
D(S)_7=Ýb

D(W)_1=Duşenbe
D(W)_2=Sişenbe
D(W)_3=Çarşenbe
D(W)_4=Penşenbe
D(W)_5=Anna
D(W)_6=Şenbe
D(W)_7=Ýekşenbe

# quarters
Q(a)_1=1Ç
Q(a)_2=2Ç
Q(a)_3=3Ç
Q(a)_4=4Ç

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1-nji çärýek
Q(w)_2=2-nji çärýek
Q(w)_3=3-nji çärýek
Q(w)_4=4-nji çärýek

Q(A)_1=1Ç
Q(A)_2=2Ç
Q(A)_3=3Ç
Q(A)_4=4Ç

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1-nji çärýek
Q(W)_2=2-nji çärýek
Q(W)_3=3-nji çärýek
Q(W)_4=4-nji çärýek

# day-period-translations
P(a)_am=go.öň
P(a)_pm=go.soň

P(n)_am=öň
P(n)_pm=soň

P(w)_am=günortadan öň
P(w)_pm=günortadan soň

P(A)_am=g.öň
P(A)_pm=g.soň

P(N)_am=öň
P(N)_pm=soň

P(W)_am=günortadan öň
P(W)_pm=günortadan soň

# eras
E(w)_0=Isadan öň
E(w|alt)_0=Biziň eramyzdan öň
E(w)_1=Isadan soň
E(w|alt)_1=Biziň eramyz

E(a)_0=B.e.öň
E(a|alt)_0=b.e.öň
E(a)_1=B.e.
E(a|alt)_1=b.e.

# format patterns
F(f)_d=d MMMM y EEEE
F(l)_d=d MMMM y
F(m)_d=d MMM y
F(s)_d=dd.MM.y

F(alt)=HH:mm:ss

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=dd.MM
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=MM.y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=y QQQ
F_yQQQQ=y QQQQ
F_yw='hepde' w, Y

I={0} - {1}

# labels of elements
L_era=era
L_year=ýyl
L_quarter=çärýek
L_month=aý
L_week=hepde
L_day=gün
L_weekday=hepdäniň güni
L_dayperiod=günortadan öň/günortadan soň
L_hour=sagat
L_minute=minut
L_second=sekunt
L_zone=sagat guşaklygy
