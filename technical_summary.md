# الملخص التقني لتطبيق معادن ليبيا

## معلومات التطبيق الأساسية

| المعلومة | القيمة |
|----------|--------|
| اسم التطبيق | معادن ليبيا (Moaden Libya) |
| Package Name | com.mahmoud.android.MoadenLibya |
| الإصدار | 3.0.7 |
| حجم APK | تم استخراجه بنجاح |
| نوع التطبيق | تطبيق إسلامي |

## بنية الملفات المستخرجة

### 1. الملفات الأساسية
```
apk_extracted/
├── AndroidManifest.xml          # ملف البيان (مشفر)
├── classes.dex                  # الكود المترجم الرئيسي
├── classes2.dex                 # كود إضافي
├── classes3.dex                 # كود إضافي
├── resources.arsc               # موارد التطبيق
└── META-INF/                    # معلومات التوقيع
```

### 2. مجلد الموارد (assets/)
```
assets/
├── Libya/                       # 99 ملف JSON للمدن الليبية
├── KSA/                         # ملفات JSON للمدن السعودية
├── athkar/                      # 14 ملف أذكار مختلفة
├── quran.hafs.json             # القرآن برواية حفص (54,118 سطر)
├── quran.qaloun.json           # القرآن برواية قالون
├── quran.qaloun2.json          # نسخة إضافية قالون
├── libya.cities.json           # قائمة المدن الليبية
├── ksa.cities.json             # قائمة المدن السعودية
├── hadeath.json                # الأحاديث النبوية
├── tafseer.json                # التفسير
└── help.json                   # ملف المساعدة
```

### 3. مجلد الموارد المرئية (res/)
```
res/
├── *.png                       # صور وأيقونات
├── *.jpg                       # صور
├── *.webp                      # صور محسنة
├── *.mp3                       # ملفات صوتية
├── *.ttf                       # خطوط عربية
├── *.otf                       # خطوط عربية
├── *.xml                       # تخطيطات وألوان
└── color/                      # ألوان النمط الليلي
```

## التقنيات والمكتبات المستخدمة

### 1. مكتبات Google
- **Google Maps API**: تحديد المواقع والقبلة
- **Google Play Services**: خدمات متنوعة
- **Firebase Analytics**: تحليل الاستخدام
- **Firebase Crashlytics**: تتبع الأخطاء
- **Firebase Installations**: إدارة التثبيت

### 2. مكتبات AndroidX
- **AndroidX Core**: الوظائف الأساسية
- **AndroidX AppCompat**: التوافق مع الإصدارات القديمة
- **AndroidX Fragment**: إدارة الشاشات
- **AndroidX Navigation**: التنقل بين الشاشات
- **AndroidX Room**: قاعدة البيانات المحلية
- **AndroidX Lifecycle**: إدارة دورة حياة التطبيق

### 3. مكتبات إضافية
- **Kotlin Coroutines**: البرمجة غير المتزامنة
- **DataStore**: تخزين الإعدادات
- **ProfileInstaller**: تحسين الأداء

## الصلاحيات المطلوبة

### صلاحيات الموقع
- `ACCESS_FINE_LOCATION`: الموقع الدقيق
- `ACCESS_COARSE_LOCATION`: الموقع التقريبي

### صلاحيات الشبكة
- `INTERNET`: الوصول للإنترنت
- `ACCESS_NETWORK_STATE`: حالة الشبكة
- `ACCESS_WIFI_STATE`: حالة WiFi

### صلاحيات الإشعارات
- `POST_NOTIFICATIONS`: إرسال الإشعارات
- `SCHEDULE_EXACT_ALARM`: جدولة المنبهات
- `RECEIVE_BOOT_COMPLETED`: التشغيل عند بدء النظام

### صلاحيات أخرى
- `VIBRATE`: الاهتزاز
- `WAKE_LOCK`: منع النوم
- `WRITE_EXTERNAL_STORAGE`: الكتابة على التخزين

## بنية التطبيق

### الأنشطة الرئيسية (Activities)
```java
com.mahmoud.android.MoadenLibya.MainActivity
com.mahmoud.android.MoadenLibya.LaunchActivity
com.mahmoud.android.MoadenLibya.quran.QuranActivity
com.mahmoud.android.MoadenLibya.qibla.QiblaJava
com.mahmoud.android.MoadenLibya.settings.SettingsActivity
com.mahmoud.android.MoadenLibya.city.CityActivity
```

### الخدمات (Services)
```java
com.mahmoud.android.MoadenLibya.util.NotificationReceiver
com.mahmoud.android.MoadenLibya.util.SilentReceiver
com.mahmoud.android.MoadenLibya.util.TimeChangedReceiver
com.mahmoud.android.MoadenLibya.util.WidgetReceiver
```

### موفري الويدجت (Widget Providers)
```java
com.mahmoud.android.MoadenLibya.widgetProviders.SmallWidgetProvider
com.mahmoud.android.MoadenLibya.widgetProviders.MediumWidgetProvider
com.mahmoud.android.MoadenLibya.widgetProviders.LargeWidgetProvider
```

## قاعدة البيانات والتخزين

### 1. ملفات JSON
- **المدن**: إحداثيات جغرافية دقيقة
- **القرآن**: نصوص كاملة بروايات متعددة
- **الأذكار**: مصنفة حسب الأوقات
- **الأحاديث**: مع المصادر

### 2. قاعدة البيانات المحلية
- **Room Database**: لتخزين البيانات المحلية
- **DataStore**: للإعدادات والتفضيلات

## الميزات التقنية

### 1. دعم اللغة العربية
- خطوط عربية مخصصة
- دعم الكتابة من اليمين لليسار (RTL)
- تشكيل النصوص القرآنية

### 2. الأداء
- تحسين الذاكرة
- تحميل البيانات بشكل تدريجي
- استخدام WebP للصور

### 3. واجهة المستخدم
- Material Design
- النمط الليلي
- ويدجت للشاشة الرئيسية

## إحصائيات المحتوى

| نوع المحتوى | العدد |
|-------------|-------|
| المدن الليبية | 99 مدينة |
| المدن السعودية | 100+ مدينة |
| آيات القرآن | 6,236 آية |
| ملفات الأذكار | 14 ملف |
| الصور والأيقونات | 500+ ملف |
| الملفات الصوتية | 50+ ملف |

## متطلبات النظام

### الحد الأدنى
- **Android**: API Level 14 (Android 4.0)
- **الذاكرة**: 50 MB مساحة فارغة
- **الشبكة**: اختيارية للتحديثات

### الموصى به
- **Android**: API Level 23+ (Android 6.0+)
- **GPS**: لتحديد الموقع الدقيق
- **الإنترنت**: للخرائط والتحديثات

## الأمان والخصوصية

### 1. التوقيع الرقمي
- التطبيق موقع رقمياً
- ملفات META-INF تحتوي على معلومات التوقيع

### 2. الصلاحيات
- طلب الصلاحيات عند الحاجة
- شفافية في استخدام البيانات

## التحديثات والصيانة

### 1. نظام التحديث
- ملفات "whats.new" للإصدارات الجديدة
- دعم التحديثات التلقائية

### 2. التتبع والتحليل
- Firebase Analytics للاستخدام
- Crashlytics لتتبع الأخطاء

## الخلاصة التقنية

تطبيق معادن ليبيا مطور بعناية باستخدام:
- **أحدث تقنيات Android**
- **مكتبات موثوقة ومستقرة**
- **بنية كود منظمة ومرنة**
- **دعم شامل للغة العربية**
- **أداء محسن للأجهزة المختلفة**

التطبيق يظهر مستوى عالي من الاحترافية في التطوير والتصميم.
