import 'package:flutter/material.dart';
import '../models/bookmark.dart';
import '../models/verse.dart';
import '../services/bookmark_service.dart';

class BookmarkProvider extends ChangeNotifier {
  final BookmarkService _bookmarkService = BookmarkService.instance;

  List<Bookmark> _bookmarks = [];
  List<Bookmark> _filteredBookmarks = [];
  bool _isLoading = false;
  String _searchQuery = '';
  BookmarkSortType _sortType = BookmarkSortType.dateDescending;

  // Getters
  List<Bookmark> get bookmarks => _filteredBookmarks.isEmpty && _searchQuery.isEmpty 
      ? _bookmarks 
      : _filteredBookmarks;
  bool get isLoading => _isLoading;
  String get searchQuery => _searchQuery;
  BookmarkSortType get sortType => _sortType;
  int get bookmarksCount => _bookmarks.length;

  /// تحميل العلامات المرجعية
  Future<void> loadBookmarks() async {
    _isLoading = true;
    notifyListeners();

    try {
      _bookmarks = await _bookmarkService.getBookmarks();
      _applySorting();
      _applyFilter();
    } catch (e) {
      debugPrint('خطأ في تحميل العلامات المرجعية: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// إضافة علامة مرجعية جديدة
  Future<bool> addBookmark({
    required String surahName,
    required Verse verse,
    required int page,
    String? note,
  }) async {
    try {
      final bookmark = Bookmark(
        id: '${surahName}_${verse.id}_${DateTime.now().millisecondsSinceEpoch}',
        surahName: surahName,
        verseId: verse.id,
        page: page,
        verseText: verse.text,
        createdAt: DateTime.now(),
        note: note,
      );

      final success = await _bookmarkService.saveBookmark(bookmark);
      if (success) {
        await loadBookmarks();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في إضافة العلامة المرجعية: $e');
      return false;
    }
  }

  /// حذف علامة مرجعية
  Future<bool> deleteBookmark(String bookmarkId) async {
    try {
      final success = await _bookmarkService.deleteBookmark(bookmarkId);
      if (success) {
        await loadBookmarks();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في حذف العلامة المرجعية: $e');
      return false;
    }
  }

  /// التحقق من وجود علامة مرجعية
  Future<bool> isBookmarked(String surahName, int verseId) async {
    return await _bookmarkService.isBookmarked(surahName, verseId);
  }

  /// تبديل حالة العلامة المرجعية
  Future<bool> toggleBookmark({
    required String surahName,
    required Verse verse,
    required int page,
    String? note,
  }) async {
    final isCurrentlyBookmarked = await isBookmarked(surahName, verse.id);
    
    if (isCurrentlyBookmarked) {
      final bookmark = await _bookmarkService.getBookmark(surahName, verse.id);
      if (bookmark != null) {
        return await deleteBookmark(bookmark.id);
      }
      return false;
    } else {
      return await addBookmark(
        surahName: surahName,
        verse: verse,
        page: page,
        note: note,
      );
    }
  }

  /// تحديث ملاحظة علامة مرجعية
  Future<bool> updateBookmarkNote(String bookmarkId, String note) async {
    try {
      final success = await _bookmarkService.updateBookmarkNote(bookmarkId, note);
      if (success) {
        await loadBookmarks();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في تحديث ملاحظة العلامة المرجعية: $e');
      return false;
    }
  }

  /// البحث في العلامات المرجعية
  void searchBookmarks(String query) {
    _searchQuery = query.trim();
    _applyFilter();
    notifyListeners();
  }

  /// مسح البحث
  void clearSearch() {
    _searchQuery = '';
    _filteredBookmarks = [];
    notifyListeners();
  }

  /// تغيير نوع الترتيب
  void setSortType(BookmarkSortType sortType) {
    if (_sortType == sortType) return;
    
    _sortType = sortType;
    _applySorting();
    _applyFilter();
    notifyListeners();
  }

  /// تطبيق الترتيب
  void _applySorting() {
    switch (_sortType) {
      case BookmarkSortType.dateAscending:
        _bookmarks.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case BookmarkSortType.dateDescending:
        _bookmarks.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case BookmarkSortType.surahName:
        _bookmarks.sort((a, b) {
          final surahComparison = a.surahName.compareTo(b.surahName);
          if (surahComparison != 0) return surahComparison;
          return a.verseId.compareTo(b.verseId);
        });
        break;
      case BookmarkSortType.pageNumber:
        _bookmarks.sort((a, b) {
          final pageComparison = a.page.compareTo(b.page);
          if (pageComparison != 0) return pageComparison;
          return a.verseId.compareTo(b.verseId);
        });
        break;
    }
  }

  /// تطبيق الفلترة
  void _applyFilter() {
    if (_searchQuery.isEmpty) {
      _filteredBookmarks = [];
      return;
    }

    final query = _searchQuery.toLowerCase();
    _filteredBookmarks = _bookmarks.where((bookmark) =>
        bookmark.surahName.toLowerCase().contains(query) ||
        bookmark.verseText.toLowerCase().contains(query) ||
        (bookmark.note?.toLowerCase().contains(query) ?? false)
    ).toList();
  }

  /// حذف جميع العلامات المرجعية
  Future<bool> clearAllBookmarks() async {
    try {
      final success = await _bookmarkService.clearAllBookmarks();
      if (success) {
        _bookmarks = [];
        _filteredBookmarks = [];
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في حذف جميع العلامات المرجعية: $e');
      return false;
    }
  }

  /// الحصول على العلامة المرجعية بالمعرف
  Bookmark? getBookmarkById(String id) {
    try {
      return _bookmarks.firstWhere((bookmark) => bookmark.id == id);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على العلامات المرجعية لسورة معينة
  List<Bookmark> getBookmarksForSurah(String surahName) {
    return _bookmarks.where((bookmark) => bookmark.surahName == surahName).toList();
  }

  /// الحصول على إحصائيات العلامات المرجعية
  Map<String, int> getBookmarkStatistics() {
    final surahCounts = <String, int>{};
    for (final bookmark in _bookmarks) {
      surahCounts[bookmark.surahName] = (surahCounts[bookmark.surahName] ?? 0) + 1;
    }
    return surahCounts;
  }

  /// الحصول على أحدث العلامات المرجعية
  List<Bookmark> getRecentBookmarks({int limit = 5}) {
    final sortedBookmarks = List<Bookmark>.from(_bookmarks);
    sortedBookmarks.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return sortedBookmarks.take(limit).toList();
  }
}

/// أنواع ترتيب العلامات المرجعية
enum BookmarkSortType {
  dateAscending,
  dateDescending,
  surahName,
  pageNumber,
}

/// امتداد للحصول على وصف نوع الترتيب
extension BookmarkSortTypeExtension on BookmarkSortType {
  String get displayName {
    switch (this) {
      case BookmarkSortType.dateAscending:
        return 'التاريخ (الأقدم أولاً)';
      case BookmarkSortType.dateDescending:
        return 'التاريخ (الأحدث أولاً)';
      case BookmarkSortType.surahName:
        return 'اسم السورة';
      case BookmarkSortType.pageNumber:
        return 'رقم الصفحة';
    }
  }

  IconData get icon {
    switch (this) {
      case BookmarkSortType.dateAscending:
      case BookmarkSortType.dateDescending:
        return Icons.access_time;
      case BookmarkSortType.surahName:
        return Icons.sort_by_alpha;
      case BookmarkSortType.pageNumber:
        return Icons.format_list_numbered;
    }
  }
}
