# months
M(a)_1=yan
M(a)_2=fev
M(a)_3=mar
M(a)_4=apr
M(a)_5=may
M(a)_6=iyn
M(a)_7=iyl
M(a)_8=avg
M(a)_9=sen
M(a)_10=okt
M(a)_11=noy
M(a)_12=dek

M(n)_1=Y
M(n)_2=F
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=I
M(n)_7=I
M(n)_8=A
M(n)_9=S
M(n)_10=O
M(n)_11=N
M(n)_12=D

M(w)_1=yanvar
M(w)_2=fevral
M(w)_3=mart
M(w)_4=aprel
M(w)_5=may
M(w)_6=iyun
M(w)_7=iyul
M(w)_8=avgust
M(w)_9=sentabr
M(w)_10=oktabr
M(w)_11=noyabr
M(w)_12=dekabr

M(A)_1=Yan
M(A)_2=Fev
M(A)_3=Mar
M(A)_4=Apr
M(A)_5=May
M(A)_6=Iyn
M(A)_7=Iyl
M(A)_8=Avg
M(A)_9=Sen
M(A)_10=Okt
M(A)_11=Noy
M(A)_12=Dek

M(N)_1=Y
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=I
M(N)_7=I
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=Yanvar
M(W)_2=Fevral
M(W)_3=Mart
M(W)_4=Aprel
M(W)_5=May
M(W)_6=Iyun
M(W)_7=Iyul
M(W)_8=Avgust
M(W)_9=Sentabr
M(W)_10=Oktabr
M(W)_11=Noyabr
M(W)_12=Dekabr

# weekdays
D(a)_1=Dush
D(a)_2=Sesh
D(a)_3=Chor
D(a)_4=Pay
D(a)_5=Jum
D(a)_6=Shan
D(a)_7=Yak

D(n)_1=D
D(n)_2=S
D(n)_3=C
D(n)_4=P
D(n)_5=J
D(n)_6=S
D(n)_7=Y

D(s)_1=Du
D(s)_2=Se
D(s)_3=Ch
D(s)_4=Pa
D(s)_5=Ju
D(s)_6=Sh
D(s)_7=Ya

D(w)_1=dushanba
D(w)_2=seshanba
D(w)_3=chorshanba
D(w)_4=payshanba
D(w)_5=juma
D(w)_6=shanba
D(w)_7=yakshanba

D(A)_1=Dush
D(A)_2=Sesh
D(A)_3=Chor
D(A)_4=Pay
D(A)_5=Jum
D(A)_6=Shan
D(A)_7=Yak

D(N)_1=D
D(N)_2=S
D(N)_3=C
D(N)_4=P
D(N)_5=J
D(N)_6=S
D(N)_7=Y

D(S)_1=Du
D(S)_2=Se
D(S)_3=Ch
D(S)_4=Pa
D(S)_5=Ju
D(S)_6=Sh
D(S)_7=Ya

D(W)_1=dushanba
D(W)_2=seshanba
D(W)_3=chorshanba
D(W)_4=payshanba
D(W)_5=juma
D(W)_6=shanba
D(W)_7=yakshanba

# quarters
Q(a)_1=1-ch
Q(a)_2=2-ch
Q(a)_3=3-ch
Q(a)_4=4-ch

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1-chorak
Q(w)_2=2-chorak
Q(w)_3=3-chorak
Q(w)_4=4-chorak

Q(A)_1=1-ch
Q(A)_2=2-ch
Q(A)_3=3-ch
Q(A)_4=4-ch

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1-chorak
Q(W)_2=2-chorak
Q(W)_3=3-chorak
Q(W)_4=4-chorak

# day-period-rules
T0600=morning1
T1100=afternoon1
T1800=evening1
T2200=night1

# day-period-translations
P(a)_midnight=yarim tun
P(a)_am=TO
P(a)_noon=tush payti
P(a)_pm=TK
P(a)_morning1=ertalab
P(a)_afternoon1=kunduzi
P(a)_evening1=kechqurun
P(a)_night1=kechasi

P(n)_midnight=yarim tun
P(n)_am=TO
P(n)_noon=tush payti
P(n)_pm=TK
P(n)_morning1=ertalab
P(n)_afternoon1=kunduzi
P(n)_evening1=kechqurun
P(n)_night1=kechasi

P(w)_midnight=yarim tun
P(w)_am=TO
P(w)_noon=tush payti
P(w)_pm=TK
P(w)_morning1=ertalab
P(w)_afternoon1=kunduzi
P(w)_evening1=kechqurun
P(w)_night1=kechasi

P(A)_midnight=yarim tun
P(A)_am=TO
P(A)_noon=tush payti
P(A)_pm=TK
P(A)_morning1=ertalab
P(A)_afternoon1=kunduzi
P(A)_evening1=kechqurun
P(A)_night1=kechasi

P(N)_midnight=yarim tun
P(N)_am=TO
P(N)_noon=tush payti
P(N)_pm=TK
P(N)_morning1=ertalab
P(N)_afternoon1=kunduzi
P(N)_evening1=kechqurun
P(N)_night1=kechasi

P(W)_midnight=yarim tun
P(W)_am=TO
P(W)_noon=tush payti
P(W)_pm=TK
P(W)_morning1=ertalab
P(W)_afternoon1=kunduzi
P(W)_evening1=kechqurun
P(W)_night1=kechasi

# eras
E(w)_0=miloddan avvalgi
E(w|alt)_0=eramizdan avvalgi
E(w)_1=milodiy
E(w|alt)_1=mil.

E(a)_0=m.a.
E(a|alt)_0=e.a.
E(a)_1=milodiy
E(a|alt)_1=mil.

# format patterns
F(f)_d=EEEE, d-MMMM, y
F(l)_d=d-MMMM, y
F(m)_d=d-MMM, y
F(s)_d=dd/MM/yy

F(alt)=H:mm:ss

F(f)_t=H:mm:ss (zzzz)
F(l)_t=H:mm:ss (z)
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1}, {0}
F(l)_dt={1}, {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Bh=B h
F_Bhm=B h:mm
F_Bhms=B h:mm:ss
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=dd/MM
F_MMMd=d-MMM
F_MMMMd=d-MMMM
F_y=y
F_yM=MM.y
F_yMMM=MMM, y
F_yMMMM=MMMM, y
F_yQQQ=y, QQQ
F_yQQQQ=y, QQQQ
F_yw=Y, w-'hafta'

I={0} – {1}

# labels of elements
L_era=era
L_year=yil
L_quarter=chorak
L_month=oy
L_week=hafta
L_day=kun
L_weekday=hafta kuni
L_dayperiod=TO/TK
L_hour=soat
L_minute=daqiqa
L_second=soniya
L_zone=vaqt mintaqasi
