import 'package:flutter/material.dart';
import '../models/surah.dart';
import '../models/surah_info.dart';
import '../services/quran_service.dart';

class QuranProvider extends ChangeNotifier {
  final QuranService _quranService = QuranService.instance;

  List<Surah> _surahs = [];
  List<SurahInfo> _surahInfoList = [];
  List<SearchResult> _searchResults = [];
  bool _isLoading = false;
  bool _isSearching = false;
  String _searchQuery = '';
  String? _errorMessage;
  int _currentPage = 1;
  int _totalPages = 604;

  // Getters
  List<Surah> get surahs => _surahs;
  List<SurahInfo> get surahInfoList => _surahInfoList;
  List<SearchResult> get searchResults => _searchResults;
  bool get isLoading => _isLoading;
  bool get isSearching => _isSearching;
  String get searchQuery => _searchQuery;
  String? get errorMessage => _errorMessage;
  int get currentPage => _currentPage;
  int get totalPages => _totalPages;

  /// تحميل بيانات القرآن
  Future<void> loadQuranData() async {
    if (_surahs.isNotEmpty) return;

    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final results = await Future.wait([
        _quranService.loadSurahs(),
        _quranService.loadSurahInfoList(),
        _quranService.getTotalPages(),
      ]);

      _surahs = results[0] as List<Surah>;
      _surahInfoList = results[1] as List<SurahInfo>;
      _totalPages = results[2] as int;

      _errorMessage = null;
    } catch (e) {
      _errorMessage = 'خطأ في تحميل بيانات القرآن: $e';
      debugPrint(_errorMessage);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// البحث في القرآن
  Future<void> searchInQuran(String query) async {
    if (query.trim() == _searchQuery.trim()) return;

    _searchQuery = query.trim();
    _isSearching = true;
    _errorMessage = null;
    notifyListeners();

    try {
      if (_searchQuery.isEmpty) {
        _searchResults = [];
      } else {
        _searchResults = await _quranService.searchInQuran(_searchQuery);
      }
      _errorMessage = null;
    } catch (e) {
      _errorMessage = 'خطأ في البحث: $e';
      _searchResults = [];
      debugPrint(_errorMessage);
    } finally {
      _isSearching = false;
      notifyListeners();
    }
  }

  /// مسح نتائج البحث
  void clearSearch() {
    _searchQuery = '';
    _searchResults = [];
    notifyListeners();
  }

  /// الحصول على السور في صفحة معينة
  List<Surah> getSurahsInPage(int page) {
    return _quranService.getSurahsInPage(page);
  }

  /// الانتقال إلى صفحة معينة
  void goToPage(int page) {
    if (page < 1 || page > _totalPages) return;
    if (page == _currentPage) return;

    _currentPage = page;
    notifyListeners();
  }

  /// الصفحة التالية
  void nextPage() {
    if (_currentPage < _totalPages) {
      _currentPage++;
      notifyListeners();
    }
  }

  /// الصفحة السابقة
  void previousPage() {
    if (_currentPage > 1) {
      _currentPage--;
      notifyListeners();
    }
  }

  /// الحصول على السورة بالاسم
  Future<Surah?> getSurahByName(String name) async {
    return await _quranService.getSurahByName(name);
  }

  /// الحصول على معلومات السورة بالاسم
  Future<SurahInfo?> getSurahInfoByName(String name) async {
    return await _quranService.getSurahInfoByName(name);
  }

  /// الانتقال إلى سورة معينة
  Future<void> goToSurah(String surahName) async {
    try {
      final page = await _quranService.getPageForSurah(surahName);
      if (page != null) {
        goToPage(page);
      }
    } catch (e) {
      _errorMessage = 'خطأ في الانتقال إلى السورة: $e';
      notifyListeners();
    }
  }

  /// الحصول على السورة الحالية
  Surah? getCurrentSurah() {
    final surahsInPage = getSurahsInPage(_currentPage);
    return surahsInPage.isNotEmpty ? surahsInPage.first : null;
  }

  /// الحصول على معلومات السورة الحالية
  SurahInfo? getCurrentSurahInfo() {
    final currentSurah = getCurrentSurah();
    if (currentSurah == null) return null;

    try {
      return _surahInfoList.firstWhere((info) => info.name == currentSurah.name);
    } catch (e) {
      return null;
    }
  }

  /// إعادة تحميل البيانات
  Future<void> refresh() async {
    _surahs = [];
    _surahInfoList = [];
    await loadQuranData();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// الحصول على تقدم القراءة (نسبة مئوية)
  double getReadingProgress() {
    if (_totalPages == 0) return 0.0;
    return (_currentPage / _totalPages) * 100;
  }

  /// الحصول على نص وصفي للصفحة الحالية
  String getCurrentPageDescription() {
    final currentSurah = getCurrentSurah();
    if (currentSurah == null) {
      return 'صفحة $_currentPage من $_totalPages';
    }

    return '${currentSurah.name} - صفحة $_currentPage من $_totalPages';
  }

  /// التحقق من وجود صفحة تالية
  bool get hasNextPage => _currentPage < _totalPages;

  /// التحقق من وجود صفحة سابقة
  bool get hasPreviousPage => _currentPage > 1;

  /// الحصول على قائمة أسماء السور
  List<String> getSurahNames() {
    return _surahInfoList.map((info) => info.name).toList();
  }

  /// الحصول على إجمالي عدد الآيات في القرآن
  int getTotalVerses() {
    return _surahInfoList.fold(0, (sum, info) => sum + info.totalVerses);
  }

  /// الحصول على إجمالي عدد السور
  int getTotalSurahs() {
    return _surahInfoList.length;
  }
}
