import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../providers/quran_provider.dart';
import '../providers/bookmark_provider.dart';
import '../models/surah.dart';
import '../models/verse.dart';
import '../widgets/verse_widget.dart';
import '../widgets/page_navigation_widget.dart';
import '../widgets/surah_header_widget.dart';

class QuranReaderScreen extends StatefulWidget {
  const QuranReaderScreen({super.key});

  @override
  State<QuranReaderScreen> createState() => _QuranReaderScreenState();
}

class _QuranReaderScreenState extends State<QuranReaderScreen> {
  late ScrollController _scrollController;
  bool _showAppBar = true;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.userScrollDirection == ScrollDirection.reverse) {
      if (_showAppBar) {
        setState(() {
          _showAppBar = false;
        });
      }
    } else if (_scrollController.position.userScrollDirection == ScrollDirection.forward) {
      if (!_showAppBar) {
        setState(() {
          _showAppBar = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<QuranProvider, SettingsProvider, BookmarkProvider>(
      builder: (context, quranProvider, settingsProvider, bookmarkProvider, child) {
        if (quranProvider.isLoading) {
          return const Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري تحميل القرآن الكريم...'),
                ],
              ),
            ),
          );
        }

        if (quranProvider.errorMessage != null) {
          return Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    quranProvider.errorMessage!,
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      quranProvider.refresh();
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            ),
          );
        }

        final surahsInPage = quranProvider.getSurahsInPage(quranProvider.currentPage);

        return Scaffold(
          appBar: _showAppBar
              ? AppBar(
                  title: Text(quranProvider.getCurrentPageDescription()),
                  actions: [
                    IconButton(
                      icon: const Icon(Icons.brightness_6),
                      onPressed: () {
                        settingsProvider.setIsDarkMode(!settingsProvider.isDarkMode);
                      },
                    ),
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        switch (value) {
                          case 'go_to_page':
                            _showGoToPageDialog(context, quranProvider);
                            break;
                          case 'font_settings':
                            _showFontSettingsDialog(context, settingsProvider);
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'go_to_page',
                          child: Row(
                            children: [
                              Icon(Icons.navigate_next),
                              SizedBox(width: 8),
                              Text('الانتقال إلى صفحة'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'font_settings',
                          child: Row(
                            children: [
                              Icon(Icons.text_fields),
                              SizedBox(width: 8),
                              Text('إعدادات الخط'),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                )
              : null,
          body: Column(
            children: [
              // شريط التقدم
              if (_showAppBar)
                LinearProgressIndicator(
                  value: quranProvider.getReadingProgress() / 100,
                  backgroundColor: Colors.grey[300],
                ),
              
              // محتوى الصفحة
              Expanded(
                child: ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: surahsInPage.length,
                  itemBuilder: (context, index) {
                    final surah = surahsInPage[index];
                    return _buildSurahContent(
                      context,
                      surah,
                      settingsProvider,
                      bookmarkProvider,
                      quranProvider,
                    );
                  },
                ),
              ),
              
              // أزرار التنقل
              PageNavigationWidget(
                currentPage: quranProvider.currentPage,
                totalPages: quranProvider.totalPages,
                onPreviousPage: quranProvider.previousPage,
                onNextPage: quranProvider.nextPage,
                onGoToPage: (page) => quranProvider.goToPage(page),
                hasNextPage: quranProvider.hasNextPage,
                hasPreviousPage: quranProvider.hasPreviousPage,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSurahContent(
    BuildContext context,
    Surah surah,
    SettingsProvider settingsProvider,
    BookmarkProvider bookmarkProvider,
    QuranProvider quranProvider,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // رأس السورة
        if (settingsProvider.showSurahHeaders)
          SurahHeaderWidget(
            surahName: surah.name,
            jozz: surah.jozz,
            page: surah.page,
          ),
        
        // الآيات
        ...surah.verses.map((verse) => VerseWidget(
          verse: verse,
          surahName: surah.name,
          page: surah.page,
          textStyle: settingsProvider.getQuranTextStyle(context),
          showVerseNumber: settingsProvider.showVerseNumbers,
          verseNumberStyle: settingsProvider.getVerseNumberTextStyle(context),
          onBookmarkToggle: (verse) async {
            await bookmarkProvider.toggleBookmark(
              surahName: surah.name,
              verse: verse,
              page: surah.page,
            );
          },
          onShare: (verse) {
            _shareVerse(verse, surah.name);
          },
          onTap: (verse) {
            _showVerseOptions(context, verse, surah, bookmarkProvider);
          },
        )),
        
        const SizedBox(height: 24),
      ],
    );
  }

  void _showGoToPageDialog(BuildContext context, QuranProvider quranProvider) {
    final controller = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الانتقال إلى صفحة'),
        content: TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            labelText: 'رقم الصفحة (1-${quranProvider.totalPages})',
            border: const OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final page = int.tryParse(controller.text);
              if (page != null && page >= 1 && page <= quranProvider.totalPages) {
                quranProvider.goToPage(page);
                Navigator.pop(context);
              }
            },
            child: const Text('انتقال'),
          ),
        ],
      ),
    );
  }

  void _showFontSettingsDialog(BuildContext context, SettingsProvider settingsProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعدادات الخط'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // حجم الخط
            Row(
              children: [
                const Text('حجم الخط:'),
                const Spacer(),
                Text('${settingsProvider.fontSize.round()}'),
              ],
            ),
            Slider(
              value: settingsProvider.fontSize,
              min: 12,
              max: 32,
              divisions: 20,
              onChanged: (value) {
                settingsProvider.setFontSize(value);
              },
            ),
            
            // تباعد الأسطر
            Row(
              children: [
                const Text('تباعد الأسطر:'),
                const Spacer(),
                Text('${settingsProvider.lineSpacing.toStringAsFixed(1)}'),
              ],
            ),
            Slider(
              value: settingsProvider.lineSpacing,
              min: 1.0,
              max: 3.0,
              divisions: 20,
              onChanged: (value) {
                settingsProvider.setLineSpacing(value);
              },
            ),
            
            // إظهار أرقام الآيات
            SwitchListTile(
              title: const Text('إظهار أرقام الآيات'),
              value: settingsProvider.showVerseNumbers,
              onChanged: (value) {
                settingsProvider.setShowVerseNumbers(value);
              },
            ),
            
            // إظهار رؤوس السور
            SwitchListTile(
              title: const Text('إظهار رؤوس السور'),
              value: settingsProvider.showSurahHeaders,
              onChanged: (value) {
                settingsProvider.setShowSurahHeaders(value);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showVerseOptions(
    BuildContext context,
    Verse verse,
    Surah surah,
    BookmarkProvider bookmarkProvider,
  ) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.bookmark),
            title: const Text('إضافة/إزالة علامة مرجعية'),
            onTap: () async {
              await bookmarkProvider.toggleBookmark(
                surahName: surah.name,
                verse: verse,
                page: surah.page,
              );
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('مشاركة الآية'),
            onTap: () {
              _shareVerse(verse, surah.name);
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.copy),
            title: const Text('نسخ الآية'),
            onTap: () {
              _copyVerse(verse, surah.name);
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  void _shareVerse(Verse verse, String surahName) {
    // TODO: تنفيذ مشاركة الآية
  }

  void _copyVerse(Verse verse, String surahName) {
    // TODO: تنفيذ نسخ الآية
  }
}
