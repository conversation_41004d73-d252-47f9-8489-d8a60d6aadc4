import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/quran_provider.dart';
import '../providers/settings_provider.dart';
import '../services/quran_service.dart';
import '../widgets/verse_widget.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  List<String> _searchHistory = [];

  @override
  void initState() {
    super.initState();
    _searchFocusNode.requestFocus();
    _loadSearchHistory();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  Future<void> _loadSearchHistory() async {
    // TODO: تحميل تاريخ البحث من التخزين المحلي
    setState(() {
      _searchHistory = [
        'الرحمن',
        'الصلاة',
        'الجنة',
        'التوبة',
        'الصبر',
      ];
    });
  }

  Future<void> _saveSearchQuery(String query) async {
    if (query.trim().isEmpty) return;
    
    setState(() {
      _searchHistory.remove(query);
      _searchHistory.insert(0, query);
      if (_searchHistory.length > 10) {
        _searchHistory = _searchHistory.take(10).toList();
      }
    });
    
    // TODO: حفظ تاريخ البحث في التخزين المحلي
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<QuranProvider, SettingsProvider>(
      builder: (context, quranProvider, settingsProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: TextField(
              controller: _searchController,
              focusNode: _searchFocusNode,
              decoration: const InputDecoration(
                hintText: 'ابحث في القرآن الكريم...',
                border: InputBorder.none,
                hintStyle: TextStyle(color: Colors.white70),
              ),
              style: const TextStyle(color: Colors.white, fontSize: 18),
              onSubmitted: (query) {
                _performSearch(query, quranProvider);
              },
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.search),
                onPressed: () {
                  _performSearch(_searchController.text, quranProvider);
                },
              ),
              if (_searchController.text.isNotEmpty)
                IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    quranProvider.clearSearch();
                  },
                ),
            ],
          ),
          body: Column(
            children: [
              // شريط الحالة
              if (quranProvider.isSearching)
                const LinearProgressIndicator(),
              
              // المحتوى
              Expanded(
                child: quranProvider.searchQuery.isEmpty
                    ? _buildSearchSuggestions(quranProvider)
                    : quranProvider.isSearching
                        ? _buildSearchingState()
                        : quranProvider.searchResults.isEmpty
                            ? _buildNoResultsState()
                            : _buildSearchResults(quranProvider, settingsProvider),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSearchSuggestions(QuranProvider quranProvider) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // نصائح البحث
        _buildSearchTips(),
        
        const SizedBox(height: 24),
        
        // تاريخ البحث
        if (_searchHistory.isNotEmpty) ...[
          _buildSectionHeader('عمليات البحث السابقة'),
          const SizedBox(height: 8),
          ..._searchHistory.map((query) => _buildHistoryItem(query, quranProvider)),
        ],
        
        const SizedBox(height: 24),
        
        // اقتراحات البحث
        _buildSectionHeader('اقتراحات للبحث'),
        const SizedBox(height: 8),
        _buildSearchSuggestionChips(quranProvider),
      ],
    );
  }

  Widget _buildSearchTips() {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb_outline, color: theme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'نصائح للبحث',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: theme.primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text('• ابحث بكلمة واحدة أو أكثر'),
            const Text('• يمكنك البحث بجزء من الكلمة'),
            const Text('• البحث غير حساس لحالة الأحرف'),
            const Text('• جرب البحث بكلمات مختلفة للحصول على نتائج أفضل'),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    final theme = Theme.of(context);
    
    return Text(
      title,
      style: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: theme.primaryColor,
      ),
    );
  }

  Widget _buildHistoryItem(String query, QuranProvider quranProvider) {
    return ListTile(
      leading: const Icon(Icons.history),
      title: Text(query),
      trailing: IconButton(
        icon: const Icon(Icons.close, size: 16),
        onPressed: () {
          setState(() {
            _searchHistory.remove(query);
          });
        },
      ),
      onTap: () {
        _searchController.text = query;
        _performSearch(query, quranProvider);
      },
    );
  }

  Widget _buildSearchSuggestionChips(QuranProvider quranProvider) {
    final suggestions = [
      'الله',
      'الرحمن الرحيم',
      'الصلاة',
      'الزكاة',
      'الحج',
      'الصيام',
      'الجنة',
      'النار',
      'التوبة',
      'الصبر',
      'الشكر',
      'الدعاء',
    ];
    
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: suggestions.map((suggestion) {
        return ActionChip(
          label: Text(suggestion),
          onPressed: () {
            _searchController.text = suggestion;
            _performSearch(suggestion, quranProvider);
          },
        );
      }).toList(),
    );
  }

  Widget _buildSearchingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('جاري البحث...'),
        ],
      ),
    );
  }

  Widget _buildNoResultsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لم يتم العثور على نتائج',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب البحث بكلمات أخرى',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              _searchController.clear();
              _searchFocusNode.requestFocus();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('بحث جديد'),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults(QuranProvider quranProvider, SettingsProvider settingsProvider) {
    final results = quranProvider.searchResults;
    
    return Column(
      children: [
        // عدد النتائج
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          color: Theme.of(context).primaryColor.withOpacity(0.1),
          child: Text(
            'تم العثور على ${_convertToArabicNumbers(results.length)} نتيجة',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
        
        // قائمة النتائج
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(8),
            itemCount: results.length,
            itemBuilder: (context, index) {
              final result = results[index];
              return _buildSearchResultCard(result, settingsProvider, quranProvider);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchResultCard(
    SearchResult result,
    SettingsProvider settingsProvider,
    QuranProvider quranProvider,
  ) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          // الانتقال إلى الآية في شاشة القراءة
          quranProvider.goToSurah(result.surah.name);
          Navigator.pop(context);
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس النتيجة
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: theme.primaryColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      result.surah.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    'آية ${_convertToArabicNumbers(result.verse.id)}',
                    style: theme.textTheme.bodySmall,
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // نص الآية مع التمييز
              RichText(
                text: _buildHighlightedText(
                  result.verse.text,
                  quranProvider.searchQuery,
                  settingsProvider.getQuranTextStyle(context),
                  theme,
                ),
                textAlign: TextAlign.justify,
              ),
              
              const SizedBox(height: 8),
              
              // معلومات إضافية
              Row(
                children: [
                  Icon(
                    Icons.menu_book,
                    size: 14,
                    color: theme.textTheme.bodySmall?.color,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'صفحة ${_convertToArabicNumbers(result.surah.page)}',
                    style: theme.textTheme.bodySmall,
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.bookmark_outline,
                    size: 14,
                    color: theme.textTheme.bodySmall?.color,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'الجزء ${_convertToArabicNumbers(result.surah.jozz)}',
                    style: theme.textTheme.bodySmall,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  TextSpan _buildHighlightedText(
    String text,
    String query,
    TextStyle baseStyle,
    ThemeData theme,
  ) {
    if (query.isEmpty) {
      return TextSpan(text: text, style: baseStyle);
    }

    final spans = <TextSpan>[];
    final regex = RegExp(RegExp.escape(query), caseSensitive: false);
    int lastMatchEnd = 0;

    for (final match in regex.allMatches(text)) {
      // النص قبل المطابقة
      if (match.start > lastMatchEnd) {
        spans.add(TextSpan(
          text: text.substring(lastMatchEnd, match.start),
          style: baseStyle,
        ));
      }

      // النص المطابق (مميز)
      spans.add(TextSpan(
        text: match.group(0),
        style: baseStyle.copyWith(
          backgroundColor: theme.primaryColor.withOpacity(0.3),
          fontWeight: FontWeight.bold,
        ),
      ));

      lastMatchEnd = match.end;
    }

    // النص بعد آخر مطابقة
    if (lastMatchEnd < text.length) {
      spans.add(TextSpan(
        text: text.substring(lastMatchEnd),
        style: baseStyle,
      ));
    }

    return TextSpan(children: spans);
  }

  void _performSearch(String query, QuranProvider quranProvider) {
    if (query.trim().isEmpty) return;
    
    _saveSearchQuery(query.trim());
    quranProvider.searchInQuran(query.trim());
    _searchFocusNode.unfocus();
  }

  /// تحويل الأرقام الإنجليزية إلى عربية
  String _convertToArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitInt = int.tryParse(digit);
      return digitInt != null ? arabicNumbers[digitInt] : digit;
    }).join();
  }
}
