import 'package:json_annotation/json_annotation.dart';

part 'verse.g.dart';

@JsonSerializable()
class Verse {
  final int id;
  final String surah;
  final String text;
  final bool start;

  const Verse({
    required this.id,
    required this.surah,
    required this.text,
    required this.start,
  });

  factory Verse.fromJson(Map<String, dynamic> json) => _$VerseFromJson(json);
  Map<String, dynamic> toJson() => _$VerseToJson(this);

  @override
  String toString() {
    return 'Verse{id: $id, surah: $surah, text: $text, start: $start}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Verse &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          surah == other.surah &&
          text == other.text &&
          start == other.start;

  @override
  int get hashCode => id.hashCode ^ surah.hashCode ^ text.hashCode ^ start.hashCode;
}
