/// إعدادات التطبيق الثابتة
class AppConfig {
  // معلومات التطبيق
  static const String appName = 'القرآن الكريم - رواية قالون';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'تطبيق لقراءة القرآن الكريم برواية قالون عن نافع';
  
  // معلومات القرآن
  static const int totalSurahs = 114;
  static const int totalVerses = 6236;
  static const int totalPages = 604;
  static const int totalJozz = 30;
  static const String quranicNarration = 'قالون عن نافع';
  
  // مسارات الملفات
  static const String quranDataPath = 'assets/data/quran.qaloun.json';
  static const String surahListPath = 'assets/data/surah.list.qaloun.json';
  
  // مفاتيح التخزين المحلي
  static const String settingsKey = 'app_settings';
  static const String bookmarksKey = 'bookmarks';
  static const String searchHistoryKey = 'search_history';
  static const String lastReadPositionKey = 'last_read_position';
  
  // الإعدادات الافتراضية
  static const double defaultFontSize = 18.0;
  static const double minFontSize = 12.0;
  static const double maxFontSize = 32.0;
  
  static const double defaultLineSpacing = 1.5;
  static const double minLineSpacing = 1.0;
  static const double maxLineSpacing = 3.0;
  
  static const bool defaultShowVerseNumbers = true;
  static const bool defaultShowSurahHeaders = true;
  static const bool defaultIsDarkMode = false;
  
  // ألوان التطبيق
  static const int primaryColorValue = 0xFF2E7D32; // أخضر إسلامي
  static const int accentColorValue = 0xFFFFB74D; // ذهبي
  
  // حدود البحث
  static const int maxSearchHistoryItems = 10;
  static const int minSearchQueryLength = 2;
  static const int maxSearchResults = 100;
  
  // حدود العلامات المرجعية
  static const int maxBookmarks = 1000;
  static const int maxBookmarkNoteLength = 500;
  
  // أسماء السور التي لا تبدأ بالبسملة
  static const List<String> surahsWithoutBasmala = [
    'الفَاتِحة',
    'التوبَة',
  ];
  
  // معلومات المطور
  static const String developerName = 'فريق تطوير التطبيقات الإسلامية';
  static const String supportEmail = '<EMAIL>';
  static const String websiteUrl = 'https://www.quranapp.com';
  
  // روابط التواصل الاجتماعي
  static const String twitterUrl = 'https://twitter.com/QuranApp';
  static const String facebookUrl = 'https://facebook.com/QuranApp';
  static const String instagramUrl = 'https://instagram.com/QuranApp';
  
  // معلومات المتجر
  static const String playStoreUrl = 'https://play.google.com/store/apps/details?id=com.example.quran_qaloun_app';
  static const String appStoreUrl = 'https://apps.apple.com/app/quran-qaloun/id123456789';
  
  // إعدادات الأداء
  static const int pageLoadBatchSize = 5; // عدد الصفحات المحملة مسبقاً
  static const int searchDebounceMs = 300; // تأخير البحث بالميلي ثانية
  static const int autoSaveIntervalMs = 5000; // فترة الحفظ التلقائي
  
  // رسائل الخطأ
  static const String errorLoadingQuran = 'خطأ في تحميل القرآن الكريم';
  static const String errorLoadingSurahs = 'خطأ في تحميل قائمة السور';
  static const String errorSavingBookmark = 'خطأ في حفظ العلامة المرجعية';
  static const String errorLoadingBookmarks = 'خطأ في تحميل العلامات المرجعية';
  static const String errorSearching = 'خطأ في البحث';
  static const String errorSavingSettings = 'خطأ في حفظ الإعدادات';
  
  // رسائل النجاح
  static const String bookmarkAdded = 'تم إضافة العلامة المرجعية';
  static const String bookmarkRemoved = 'تم حذف العلامة المرجعية';
  static const String bookmarkUpdated = 'تم تحديث العلامة المرجعية';
  static const String settingsSaved = 'تم حفظ الإعدادات';
  
  // رسائل التأكيد
  static const String confirmDeleteBookmark = 'هل أنت متأكد من حذف هذه العلامة المرجعية؟';
  static const String confirmDeleteAllBookmarks = 'هل أنت متأكد من حذف جميع العلامات المرجعية؟';
  static const String confirmResetSettings = 'هل تريد استعادة الإعدادات الافتراضية؟';
  
  // نصائح للمستخدم
  static const List<String> searchTips = [
    'ابحث بكلمة واحدة أو أكثر',
    'يمكنك البحث بجزء من الكلمة',
    'البحث غير حساس لحالة الأحرف',
    'جرب البحث بكلمات مختلفة للحصول على نتائج أفضل',
  ];
  
  static const List<String> readingTips = [
    'اضغط على آية لإضافتها للعلامات المرجعية',
    'استخدم أزرار التنقل للانتقال بين الصفحات',
    'يمكنك تخصيص حجم الخط من الإعدادات',
    'فعل النمط الليلي لراحة العينين',
  ];
  
  // اقتراحات البحث
  static const List<String> searchSuggestions = [
    'الله',
    'الرحمن الرحيم',
    'الصلاة',
    'الزكاة',
    'الحج',
    'الصيام',
    'الجنة',
    'النار',
    'التوبة',
    'الصبر',
    'الشكر',
    'الدعاء',
    'الإيمان',
    'التقوى',
    'الهداية',
  ];
  
  // معلومات الرواية
  static const String narratorInfo = '''
رواية قالون عن نافع:
• قالون: عيسى بن مينا بن وردان الزرقي المدني
• نافع: نافع بن عبد الرحمن بن أبي نعيم الليثي المدني
• من القراءات السبع المتواترة
• منتشرة في شمال أفريقيا وبعض البلدان العربية
''';
  
  // معلومات حقوق الطبع
  static const String copyrightInfo = '''
النص القرآني مأخوذ من المصحف الشريف
برواية قالون عن نافع المدني
جميع الحقوق محفوظة
''';
  
  // إعدادات التطبيق المتقدمة
  static const bool enableAnalytics = false; // تعطيل التحليلات افتراضياً
  static const bool enableCrashReporting = false; // تعطيل تقارير الأخطاء افتراضياً
  static const bool enableAutoBackup = true; // تفعيل النسخ الاحتياطي التلقائي
  
  // فحص صحة الإعدادات
  static bool isValidFontSize(double fontSize) {
    return fontSize >= minFontSize && fontSize <= maxFontSize;
  }
  
  static bool isValidLineSpacing(double lineSpacing) {
    return lineSpacing >= minLineSpacing && lineSpacing <= maxLineSpacing;
  }
  
  static bool isValidSearchQuery(String query) {
    return query.trim().length >= minSearchQueryLength;
  }
  
  static bool isValidBookmarkNote(String note) {
    return note.length <= maxBookmarkNoteLength;
  }
  
  // تحويل الأرقام إلى العربية
  static String toArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitInt = int.tryParse(digit);
      return digitInt != null ? arabicNumbers[digitInt] : digit;
    }).join();
  }
  
  // تحويل الأرقام من العربية
  static int? fromArabicNumbers(String arabicNumber) {
    const arabicToEnglish = {
      '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
      '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
    };
    
    final englishNumber = arabicNumber.split('').map((char) {
      return arabicToEnglish[char] ?? char;
    }).join();
    
    return int.tryParse(englishNumber);
  }
}
