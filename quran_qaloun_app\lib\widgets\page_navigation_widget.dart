import 'package:flutter/material.dart';

class PageNavigationWidget extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final VoidCallback onPreviousPage;
  final VoidCallback onNextPage;
  final Function(int) onGoToPage;
  final bool hasNextPage;
  final bool hasPreviousPage;

  const PageNavigationWidget({
    super.key,
    required this.currentPage,
    required this.totalPages,
    required this.onPreviousPage,
    required this.onNextPage,
    required this.onGoToPage,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // زر الصفحة السابقة
            IconButton(
              onPressed: hasPreviousPage ? onPreviousPage : null,
              icon: const Icon(Icons.chevron_right),
              tooltip: 'الصفحة السابقة',
            ),
            
            const SizedBox(width: 8),
            
            // معلومات الصفحة الحالية
            Expanded(
              child: GestureDetector(
                onTap: () => _showPageSelector(context),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: theme.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: theme.primaryColor.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.menu_book,
                        size: 16,
                        color: theme.primaryColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'صفحة ${_convertToArabicNumbers(currentPage)} من ${_convertToArabicNumbers(totalPages)}',
                        style: TextStyle(
                          color: theme.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Icon(
                        Icons.keyboard_arrow_down,
                        size: 16,
                        color: theme.primaryColor,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            const SizedBox(width: 8),
            
            // زر الصفحة التالية
            IconButton(
              onPressed: hasNextPage ? onNextPage : null,
              icon: const Icon(Icons.chevron_left),
              tooltip: 'الصفحة التالية',
            ),
          ],
        ),
      ),
    );
  }

  void _showPageSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => PageSelectorBottomSheet(
        currentPage: currentPage,
        totalPages: totalPages,
        onPageSelected: onGoToPage,
      ),
    );
  }

  /// تحويل الأرقام الإنجليزية إلى عربية
  String _convertToArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitInt = int.tryParse(digit);
      return digitInt != null ? arabicNumbers[digitInt] : digit;
    }).join();
  }
}

class PageSelectorBottomSheet extends StatefulWidget {
  final int currentPage;
  final int totalPages;
  final Function(int) onPageSelected;

  const PageSelectorBottomSheet({
    super.key,
    required this.currentPage,
    required this.totalPages,
    required this.onPageSelected,
  });

  @override
  State<PageSelectorBottomSheet> createState() => _PageSelectorBottomSheetState();
}

class _PageSelectorBottomSheetState extends State<PageSelectorBottomSheet> {
  late TextEditingController _controller;
  late int _selectedPage;

  @override
  void initState() {
    super.initState();
    _selectedPage = widget.currentPage;
    _controller = TextEditingController(text: widget.currentPage.toString());
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // مقبض السحب
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // العنوان
          Text(
            'الانتقال إلى صفحة',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 20),
          
          // شريط التمرير
          Row(
            children: [
              Text('١'),
              Expanded(
                child: Slider(
                  value: _selectedPage.toDouble(),
                  min: 1,
                  max: widget.totalPages.toDouble(),
                  divisions: widget.totalPages - 1,
                  label: _selectedPage.toString(),
                  onChanged: (value) {
                    setState(() {
                      _selectedPage = value.round();
                      _controller.text = _selectedPage.toString();
                    });
                  },
                ),
              ),
              Text(_convertToArabicNumbers(widget.totalPages)),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // حقل إدخال رقم الصفحة
          TextField(
            controller: _controller,
            keyboardType: TextInputType.number,
            textAlign: TextAlign.center,
            decoration: InputDecoration(
              labelText: 'رقم الصفحة',
              border: const OutlineInputBorder(),
              suffixText: '/ ${widget.totalPages}',
            ),
            onChanged: (value) {
              final page = int.tryParse(value);
              if (page != null && page >= 1 && page <= widget.totalPages) {
                setState(() {
                  _selectedPage = page;
                });
              }
            },
          ),
          
          const SizedBox(height: 20),
          
          // أزرار سريعة
          Wrap(
            spacing: 8,
            children: [
              _buildQuickPageButton(context, 1, 'البداية'),
              _buildQuickPageButton(context, widget.totalPages ~/ 4, 'الربع الأول'),
              _buildQuickPageButton(context, widget.totalPages ~/ 2, 'المنتصف'),
              _buildQuickPageButton(context, (widget.totalPages * 3) ~/ 4, 'الربع الثالث'),
              _buildQuickPageButton(context, widget.totalPages, 'النهاية'),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // أزرار الإجراء
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('إلغاء'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: _selectedPage >= 1 && _selectedPage <= widget.totalPages
                      ? () {
                          widget.onPageSelected(_selectedPage);
                          Navigator.pop(context);
                        }
                      : null,
                  child: const Text('انتقال'),
                ),
              ),
            ],
          ),
          
          // مساحة إضافية للوحة المفاتيح
          SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
        ],
      ),
    );
  }

  Widget _buildQuickPageButton(BuildContext context, int page, String label) {
    final theme = Theme.of(context);
    final isSelected = page == _selectedPage;
    
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _selectedPage = page;
            _controller.text = page.toString();
          });
        }
      },
      selectedColor: theme.primaryColor.withOpacity(0.2),
      checkmarkColor: theme.primaryColor,
    );
  }

  /// تحويل الأرقام الإنجليزية إلى عربية
  String _convertToArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitInt = int.tryParse(digit);
      return digitInt != null ? arabicNumbers[digitInt] : digit;
    }).join();
  }
}
