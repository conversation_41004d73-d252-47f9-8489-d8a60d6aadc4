import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/verse.dart';
import '../providers/bookmark_provider.dart';

class VerseWidget extends StatefulWidget {
  final Verse verse;
  final String surahName;
  final int page;
  final TextStyle textStyle;
  final bool showVerseNumber;
  final TextStyle verseNumberStyle;
  final Function(Verse) onBookmarkToggle;
  final Function(Verse) onShare;
  final Function(Verse) onTap;

  const VerseWidget({
    super.key,
    required this.verse,
    required this.surahName,
    required this.page,
    required this.textStyle,
    required this.showVerseNumber,
    required this.verseNumberStyle,
    required this.onBookmarkToggle,
    required this.onShare,
    required this.onTap,
  });

  @override
  State<VerseWidget> createState() => _VerseWidgetState();
}

class _VerseWidgetState extends State<VerseWidget> with SingleTickerProviderStateMixin {
  bool _isBookmarked = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _checkBookmarkStatus();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _checkBookmarkStatus() async {
    final bookmarkProvider = context.read<BookmarkProvider>();
    final isBookmarked = await bookmarkProvider.isBookmarked(widget.surahName, widget.verse.id);
    if (mounted) {
      setState(() {
        _isBookmarked = isBookmarked;
      });
    }
  }

  void _onTapDown(TapDownDetails details) {
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _onTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            onTap: () => widget.onTap(widget.verse),
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 4),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _isBookmarked 
                    ? theme.primaryColor.withOpacity(0.1)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
                border: _isBookmarked 
                    ? Border.all(
                        color: theme.primaryColor.withOpacity(0.3),
                        width: 1,
                      )
                    : null,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // نص الآية
                  RichText(
                    textAlign: TextAlign.justify,
                    textDirection: TextDirection.rtl,
                    text: TextSpan(
                      children: [
                        // بسملة في بداية السورة
                        if (widget.verse.start && widget.verse.id == 1 && widget.surahName != 'الفَاتِحة' && widget.surahName != 'التوبَة')
                          TextSpan(
                            text: 'بِسۡمِ ٱللَّهِ ٱلرَّحۡمَٰنِ ٱلرَّحِيمِ\n',
                            style: widget.textStyle.copyWith(
                              color: theme.primaryColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        
                        // نص الآية
                        TextSpan(
                          text: widget.verse.text,
                          style: widget.textStyle,
                        ),
                        
                        // رقم الآية
                        if (widget.showVerseNumber)
                          TextSpan(
                            text: ' ﴿${_convertToArabicNumbers(widget.verse.id)}﴾',
                            style: widget.verseNumberStyle,
                          ),
                      ],
                    ),
                  ),
                  
                  // أزرار الإجراءات (تظهر عند الضغط المطول)
                  if (_isBookmarked)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          _buildActionButton(
                            icon: Icons.bookmark,
                            color: theme.primaryColor,
                            onPressed: () async {
                              await widget.onBookmarkToggle(widget.verse);
                              await _checkBookmarkStatus();
                            },
                          ),
                          const SizedBox(width: 8),
                          _buildActionButton(
                            icon: Icons.share,
                            color: theme.colorScheme.secondary,
                            onPressed: () => widget.onShare(widget.verse),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: IconButton(
        icon: Icon(icon, size: 16),
        color: color,
        onPressed: onPressed,
        padding: EdgeInsets.zero,
      ),
    );
  }

  /// تحويل الأرقام الإنجليزية إلى عربية
  String _convertToArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitInt = int.tryParse(digit);
      return digitInt != null ? arabicNumbers[digitInt] : digit;
    }).join();
  }
}

/// ويدجت مخصص لعرض الآية مع تأثيرات بصرية
class AnimatedVerseWidget extends StatefulWidget {
  final Verse verse;
  final String surahName;
  final TextStyle textStyle;
  final bool showVerseNumber;
  final TextStyle verseNumberStyle;
  final bool isHighlighted;
  final Function(Verse) onTap;

  const AnimatedVerseWidget({
    super.key,
    required this.verse,
    required this.surahName,
    required this.textStyle,
    required this.showVerseNumber,
    required this.verseNumberStyle,
    this.isHighlighted = false,
    required this.onTap,
  });

  @override
  State<AnimatedVerseWidget> createState() => _AnimatedVerseWidgetState();
}

class _AnimatedVerseWidgetState extends State<AnimatedVerseWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    if (widget.isHighlighted) {
      _startHighlightAnimation();
    }
  }

  @override
  void didUpdateWidget(AnimatedVerseWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isHighlighted && !oldWidget.isHighlighted) {
      _startHighlightAnimation();
    }
  }

  void _startHighlightAnimation() {
    final theme = Theme.of(context);
    _colorAnimation = ColorTween(
      begin: theme.primaryColor.withOpacity(0.3),
      end: Colors.transparent,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    _controller.forward().then((_) {
      _controller.reverse();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            color: widget.isHighlighted ? _colorAnimation.value : null,
            borderRadius: BorderRadius.circular(8),
          ),
          child: VerseWidget(
            verse: widget.verse,
            surahName: widget.surahName,
            page: 0, // سيتم تمرير القيمة الصحيحة من الشاشة الأب
            textStyle: widget.textStyle,
            showVerseNumber: widget.showVerseNumber,
            verseNumberStyle: widget.verseNumberStyle,
            onBookmarkToggle: (_) {},
            onShare: (_) {},
            onTap: widget.onTap,
          ),
        );
      },
    );
  }
}
