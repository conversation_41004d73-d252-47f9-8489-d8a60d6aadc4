# months
M(a)_1=jan.
M(a)_2=feb.
M(a)_3=mars
M(a)_4=apr.
M(a)_5=maj
M(a)_6=juni
M(a)_7=juli
M(a)_8=aug.
M(a)_9=sep.
M(a)_10=okt.
M(a)_11=nov.
M(a)_12=dec.

M(n)_1=J
M(n)_2=F
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=J
M(n)_7=J
M(n)_8=A
M(n)_9=S
M(n)_10=O
M(n)_11=N
M(n)_12=D

M(w)_1=januari
M(w)_2=februari
M(w)_3=mars
M(w)_4=april
M(w)_5=maj
M(w)_6=juni
M(w)_7=juli
M(w)_8=augusti
M(w)_9=september
M(w)_10=oktober
M(w)_11=november
M(w)_12=december

M(A)_1=jan.
M(A)_2=feb.
M(A)_3=mars
M(A)_4=apr.
M(A)_5=maj
M(A)_6=juni
M(A)_7=juli
M(A)_8=aug.
M(A)_9=sep.
M(A)_10=okt.
M(A)_11=nov.
M(A)_12=dec.

M(N)_1=J
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=J
M(N)_7=J
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=januari
M(W)_2=februari
M(W)_3=mars
M(W)_4=april
M(W)_5=maj
M(W)_6=juni
M(W)_7=juli
M(W)_8=augusti
M(W)_9=september
M(W)_10=oktober
M(W)_11=november
M(W)_12=december

# weekdays
D(a)_1=mån
D(a)_2=tis
D(a)_3=ons
D(a)_4=tors
D(a)_5=fre
D(a)_6=lör
D(a)_7=sön

D(n)_1=M
D(n)_2=T
D(n)_3=O
D(n)_4=T
D(n)_5=F
D(n)_6=L
D(n)_7=S

D(s)_1=må
D(s)_2=ti
D(s)_3=on
D(s)_4=to
D(s)_5=fr
D(s)_6=lö
D(s)_7=sö

D(w)_1=måndag
D(w)_2=tisdag
D(w)_3=onsdag
D(w)_4=torsdag
D(w)_5=fredag
D(w)_6=lördag
D(w)_7=söndag

D(A)_1=mån
D(A)_2=tis
D(A)_3=ons
D(A)_4=tors
D(A)_5=fre
D(A)_6=lör
D(A)_7=sön

D(N)_1=M
D(N)_2=T
D(N)_3=O
D(N)_4=T
D(N)_5=F
D(N)_6=L
D(N)_7=S

D(S)_1=må
D(S)_2=ti
D(S)_3=on
D(S)_4=to
D(S)_5=fr
D(S)_6=lö
D(S)_7=sö

D(W)_1=måndag
D(W)_2=tisdag
D(W)_3=onsdag
D(W)_4=torsdag
D(W)_5=fredag
D(W)_6=lördag
D(W)_7=söndag

# quarters
Q(a)_1=K1
Q(a)_2=K2
Q(a)_3=K3
Q(a)_4=K4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1:a kvartalet
Q(w)_2=2:a kvartalet
Q(w)_3=3:e kvartalet
Q(w)_4=4:e kvartalet

Q(A)_1=K1
Q(A)_2=K2
Q(A)_3=K3
Q(A)_4=K4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1:a kvartalet
Q(W)_2=2:a kvartalet
Q(W)_3=3:e kvartalet
Q(W)_4=4:e kvartalet

# day-period-rules
T0000=night1
T0500=morning1
T1000=morning2
T1200=afternoon1
T1800=evening1

# day-period-translations
P(a)_midnight=midnatt
P(a)_am=fm
P(a)_pm=em
P(a)_morning1=på morg.
P(a)_morning2=på förm.
P(a)_afternoon1=på efterm.
P(a)_evening1=på kvällen
P(a)_night1=på natten

P(n)_midnight=midn.
P(n)_am=fm
P(n)_pm=em
P(n)_morning1=på morg.
P(n)_morning2=på förm.
P(n)_afternoon1=på efterm.
P(n)_evening1=på kvällen
P(n)_night1=på natten

P(w)_midnight=midnatt
P(w)_am=fm
P(w)_pm=em
P(w)_morning1=på morgonen
P(w)_morning2=på förmiddagen
P(w)_afternoon1=på eftermiddagen
P(w)_evening1=på kvällen
P(w)_night1=på natten

P(A)_midnight=midnatt
P(A)_am=f.m.
P(A)_pm=e.m.
P(A)_morning1=morgon
P(A)_morning2=förm.
P(A)_afternoon1=efterm.
P(A)_evening1=kväll
P(A)_night1=natt

P(N)_midnight=midn.
P(N)_am=fm
P(N)_pm=em
P(N)_morning1=morg.
P(N)_morning2=förm.
P(N)_afternoon1=efterm.
P(N)_evening1=kväll
P(N)_night1=natt

P(W)_midnight=midnatt
P(W)_am=förmiddag
P(W)_pm=eftermiddag
P(W)_morning1=morgon
P(W)_morning2=förmiddag
P(W)_afternoon1=eftermiddag
P(W)_evening1=kväll
P(W)_night1=natt

# eras
E(w)_0=före Kristus
E(w|alt)_0=före västerländsk tideräkning
E(w)_1=efter Kristus
E(w|alt)_1=västerländsk tideräkning

E(a)_0=f.Kr.
E(a|alt)_0=f.v.t.
E(a)_1=e.Kr.
E(a|alt)_1=v.t.

E(n)_0=f.Kr.
E(n|alt)_0=f.v.t.
E(n)_1=e.Kr.
E(n|alt)_1=v.t.

# format patterns
F(f)_d=EEEE d MMMM y
F(l)_d=d MMMM y
F(m)_d=d MMM y

F(alt)='kl 'H:mm

F(f)_t='kl'. HH.mm.ss zzzz
F(l)_t=HH.mm.ss z
F(m)_t=HH.mm.ss
F(s)_t=HH.mm

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d/M
F_MMd=d/M
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=y-MM
F_yMM=y-MM
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw='vecka' w, Y

I={0} – {1}

# labels of elements
L_era=era
L_year=år
L_quarter=kvartal
L_month=månad
L_week=vecka
L_day=dag
L_weekday=veckodag
L_dayperiod=fm/em
L_hour=timme
L_minute=minut
L_second=sekund
L_zone=tidszon
