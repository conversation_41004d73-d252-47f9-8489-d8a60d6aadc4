# future
Y+1=an {0} Joer
M+1=an {0} Mount
W+1=an {0} Woch
D+1=an {0} Dag
H+1=an {0} Stonn
N+1=an {0} Minutt
S+1=an {0} Sekonn

Y+5=a(n) {0} Joer
M+5=a(n) {0} Méint
W+5=a(n) {0} Wochen
D+5=a(n) {0} Deeg
H+5=a(n) {0} Stonnen
N+5=a(n) {0} Minutten
S+5=a(n) {0} Sekonnen

# past
Y-1=virun {0} Joer
M-1=virun {0} Mount
W-1=virun {0} Woch
D-1=virun {0} Dag
H-1=virun {0} Stonn
N-1=virun {0} Minutt
S-1=virun {0} Sekonn

Y-5=viru(n) {0} Joer
M-5=viru(n) {0} Méint
W-5=viru(n) {0} Wochen
D-5=viru(n) {0} Deeg
H-5=viru(n) {0} Stonnen
N-5=viru(n) {0} Minutten
S-5=viru(n) {0} Sekonnen

# current time
now=elo

# future (short)
y+1=an {0} J.
m+1=an {0} M.
w+1=an {0} W.
d+1=an {0} D.
h+1=an {0} St.
n+1=an {0} Min.
s+1=an {0} Sek.

y+5=a(n) {0} J.
m+5=a(n) {0} M.
w+5=a(n) {0} W.
d+5=a(n) {0} D.
h+5=a(n) {0} St.
n+5=a(n) {0} Min.
s+5=a(n) {0} Sek.

# past (short)
y-1=virun {0} J.
m-1=virun {0} M.
w-1=virun {0} W.
d-1=virun {0} D.
h-1=virun {0} St.
n-1=virun {0} Min.
s-1=virun {0} Sek.

y-5=viru(n) {0} J.
m-5=viru(n) {0} M.
w-5=viru(n) {0} W.
d-5=viru(n) {0} D.
h-5=viru(n) {0} St.
n-5=viru(n) {0} Min.
s-5=viru(n) {0} Sek.

# relative day
yesterday=gëschter
today=haut
tomorrow=muer

mon-=leschte Méindeg
mon+=nächste Méindeg
tue-=leschten Dënschdeg
tue+=nächsten Dënschdeg
wed-=leschte Mëttwoch
wed+=nächste Mëttwoch
thu-=leschten Donneschdeg
thu+=nächsten Donneschdeg
fri-=leschte Freideg
fri+=nächste Freideg
sat-=leschte Samschdeg
sat+=nächste Samschdeg
sun-=leschte Sonndeg
sun+=nächste Sonndeg
