import 'package:json_annotation/json_annotation.dart';

part 'bookmark.g.dart';

@JsonSerializable()
class Bookmark {
  final String id;
  final String surahName;
  final int verseId;
  final int page;
  final String verseText;
  final DateTime createdAt;
  final String? note;

  const Bookmark({
    required this.id,
    required this.surahName,
    required this.verseId,
    required this.page,
    required this.verseText,
    required this.createdAt,
    this.note,
  });

  factory Bookmark.fromJson(Map<String, dynamic> json) => _$BookmarkFromJson(json);
  Map<String, dynamic> toJson() => _$BookmarkToJson(this);

  Bookmark copyWith({
    String? id,
    String? surahName,
    int? verseId,
    int? page,
    String? verseText,
    DateTime? createdAt,
    String? note,
  }) {
    return Bookmark(
      id: id ?? this.id,
      surahName: surahName ?? this.surahName,
      verseId: verseId ?? this.verseId,
      page: page ?? this.page,
      verseText: verseText ?? this.verseText,
      createdAt: createdAt ?? this.createdAt,
      note: note ?? this.note,
    );
  }

  @override
  String toString() {
    return 'Bookmark{id: $id, surahName: $surahName, verseId: $verseId, page: $page}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Bookmark &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
