import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/bookmark_provider.dart';
import '../providers/quran_provider.dart';
import '../models/bookmark.dart';

class BookmarksScreen extends StatefulWidget {
  const BookmarksScreen({super.key});

  @override
  State<BookmarksScreen> createState() => _BookmarksScreenState();
}

class _BookmarksScreenState extends State<BookmarksScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<BookmarkProvider, QuranProvider>(
      builder: (context, bookmarkProvider, quranProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('العلامات المرجعية'),
            actions: [
              // زر الترتيب
              PopupMenuButton<BookmarkSortType>(
                icon: const Icon(Icons.sort),
                onSelected: (sortType) {
                  bookmarkProvider.setSortType(sortType);
                },
                itemBuilder: (context) => BookmarkSortType.values.map((sortType) {
                  return PopupMenuItem(
                    value: sortType,
                    child: Row(
                      children: [
                        Icon(sortType.icon),
                        const SizedBox(width: 8),
                        Text(sortType.displayName),
                        if (bookmarkProvider.sortType == sortType)
                          const Padding(
                            padding: EdgeInsets.only(right: 8),
                            child: Icon(Icons.check, size: 16),
                          ),
                      ],
                    ),
                  );
                }).toList(),
              ),
              
              // زر المزيد
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'clear_all':
                      _showClearAllDialog(context, bookmarkProvider);
                      break;
                    case 'export':
                      _exportBookmarks(bookmarkProvider);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'export',
                    child: Row(
                      children: [
                        Icon(Icons.share),
                        SizedBox(width: 8),
                        Text('تصدير العلامات'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'clear_all',
                    child: Row(
                      children: [
                        Icon(Icons.delete_sweep, color: Colors.red),
                        SizedBox(width: 8),
                        Text('حذف جميع العلامات', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(60),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'ابحث في العلامات المرجعية...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: bookmarkProvider.searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              bookmarkProvider.clearSearch();
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                  ),
                  onChanged: (value) {
                    bookmarkProvider.searchBookmarks(value);
                  },
                ),
              ),
            ),
          ),
          body: bookmarkProvider.isLoading
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('جاري تحميل العلامات المرجعية...'),
                    ],
                  ),
                )
              : bookmarkProvider.bookmarks.isEmpty
                  ? _buildEmptyState(context)
                  : Column(
                      children: [
                        // إحصائيات العلامات المرجعية
                        _buildBookmarkStats(context, bookmarkProvider),
                        
                        // قائمة العلامات المرجعية
                        Expanded(
                          child: ListView.builder(
                            padding: const EdgeInsets.all(8),
                            itemCount: bookmarkProvider.bookmarks.length,
                            itemBuilder: (context, index) {
                              final bookmark = bookmarkProvider.bookmarks[index];
                              return _buildBookmarkCard(
                                context,
                                bookmark,
                                bookmarkProvider,
                                quranProvider,
                              );
                            },
                          ),
                        ),
                      ],
                    ),
        );
      },
    );
  }

  Widget _buildBookmarkStats(BuildContext context, BookmarkProvider bookmarkProvider) {
    final theme = Theme.of(context);
    final stats = bookmarkProvider.getBookmarkStatistics();
    
    if (stats.isEmpty) return const SizedBox.shrink();
    
    return Container(
      margin: const EdgeInsets.all(8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.primaryColor.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics,
                color: theme.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'إحصائيات العلامات المرجعية',
                style: TextStyle(
                  color: theme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                context,
                Icons.bookmark,
                '${_convertToArabicNumbers(bookmarkProvider.bookmarksCount)}',
                'إجمالي العلامات',
              ),
              _buildStatItem(
                context,
                Icons.menu_book,
                '${_convertToArabicNumbers(stats.length)}',
                'سور محفوظة',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, IconData icon, String number, String label) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        Icon(
          icon,
          color: theme.primaryColor,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          number,
          style: TextStyle(
            color: theme.primaryColor,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: theme.primaryColor.withOpacity(0.7),
            fontSize: 10,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildBookmarkCard(
    BuildContext context,
    Bookmark bookmark,
    BookmarkProvider bookmarkProvider,
    QuranProvider quranProvider,
  ) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          // الانتقال إلى الآية
          quranProvider.goToPage(bookmark.page);
          
          // العودة إلى شاشة القراءة
          DefaultTabController.of(context).animateTo(0);
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: theme.primaryColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      bookmark.surahName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const Spacer(),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit_note':
                          _showEditNoteDialog(context, bookmark, bookmarkProvider);
                          break;
                        case 'delete':
                          _showDeleteDialog(context, bookmark, bookmarkProvider);
                          break;
                        case 'share':
                          _shareBookmark(bookmark);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit_note',
                        child: Row(
                          children: [
                            Icon(Icons.edit_note),
                            SizedBox(width: 8),
                            Text('تعديل الملاحظة'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'share',
                        child: Row(
                          children: [
                            Icon(Icons.share),
                            SizedBox(width: 8),
                            Text('مشاركة'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // نص الآية
              Text(
                bookmark.verseText,
                style: const TextStyle(
                  fontSize: 16,
                  height: 1.8,
                ),
                textAlign: TextAlign.justify,
              ),
              
              const SizedBox(height: 12),
              
              // معلومات إضافية
              Row(
                children: [
                  Icon(
                    Icons.format_quote,
                    size: 14,
                    color: theme.textTheme.bodySmall?.color,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'آية ${_convertToArabicNumbers(bookmark.verseId)}',
                    style: theme.textTheme.bodySmall,
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.menu_book,
                    size: 14,
                    color: theme.textTheme.bodySmall?.color,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'صفحة ${_convertToArabicNumbers(bookmark.page)}',
                    style: theme.textTheme.bodySmall,
                  ),
                  const Spacer(),
                  Text(
                    _formatDate(bookmark.createdAt),
                    style: theme.textTheme.bodySmall,
                  ),
                ],
              ),
              
              // الملاحظة
              if (bookmark.note != null && bookmark.note!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: theme.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.note,
                        size: 16,
                        color: theme.primaryColor,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          bookmark.note!,
                          style: TextStyle(
                            fontSize: 12,
                            color: theme.primaryColor,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bookmark_border,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد علامات مرجعية',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على آية في شاشة القراءة لإضافة علامة مرجعية',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // العودة إلى شاشة القراءة
              DefaultTabController.of(context).animateTo(0);
            },
            icon: const Icon(Icons.menu_book),
            label: const Text('ابدأ القراءة'),
          ),
        ],
      ),
    );
  }

  void _showEditNoteDialog(
    BuildContext context,
    Bookmark bookmark,
    BookmarkProvider bookmarkProvider,
  ) {
    final controller = TextEditingController(text: bookmark.note ?? '');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل الملاحظة'),
        content: TextField(
          controller: controller,
          maxLines: 3,
          decoration: const InputDecoration(
            hintText: 'اكتب ملاحظتك هنا...',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              await bookmarkProvider.updateBookmarkNote(bookmark.id, controller.text);
              Navigator.pop(context);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(
    BuildContext context,
    Bookmark bookmark,
    BookmarkProvider bookmarkProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف العلامة المرجعية'),
        content: const Text('هل أنت متأكد من حذف هذه العلامة المرجعية؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              await bookmarkProvider.deleteBookmark(bookmark.id);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showClearAllDialog(BuildContext context, BookmarkProvider bookmarkProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف جميع العلامات المرجعية'),
        content: const Text('هل أنت متأكد من حذف جميع العلامات المرجعية؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              await bookmarkProvider.clearAllBookmarks();
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف الكل'),
          ),
        ],
      ),
    );
  }

  void _shareBookmark(Bookmark bookmark) {
    // TODO: تنفيذ مشاركة العلامة المرجعية
  }

  void _exportBookmarks(BookmarkProvider bookmarkProvider) {
    // TODO: تنفيذ تصدير العلامات المرجعية
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${_convertToArabicNumbers(difference.inDays)} أيام';
    } else {
      return '${_convertToArabicNumbers(date.day)}/${_convertToArabicNumbers(date.month)}/${_convertToArabicNumbers(date.year)}';
    }
  }

  /// تحويل الأرقام الإنجليزية إلى عربية
  String _convertToArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitInt = int.tryParse(digit);
      return digitInt != null ? arabicNumbers[digitInt] : digit;
    }).join();
  }
}
