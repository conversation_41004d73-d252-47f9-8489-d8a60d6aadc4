# months
M(a)_1=జన
M(a)_2=ఫిబ్ర
M(a)_3=మార్చి
M(a)_4=ఏప్రి
M(a)_5=మే
M(a)_6=జూన్
M(a)_7=జులై
M(a)_8=ఆగ
M(a)_9=సెప్టెం
M(a)_10=అక్టో
M(a)_11=నవం
M(a)_12=డిసెం

M(n)_1=జ
M(n)_2=ఫి
M(n)_3=మా
M(n)_4=ఏ
M(n)_5=మే
M(n)_6=జూ
M(n)_7=జు
M(n)_8=ఆ
M(n)_9=సె
M(n)_10=అ
M(n)_11=న
M(n)_12=డి

M(w)_1=జనవరి
M(w)_2=ఫిబ్రవరి
M(w)_3=మార్చి
M(w)_4=ఏప్రిల్
M(w)_5=మే
M(w)_6=జూన్
M(w)_7=జులై
M(w)_8=ఆగస్టు
M(w)_9=సెప్టెంబర్
M(w)_10=అక్టోబర్
M(w)_11=నవంబర్
M(w)_12=డిసెంబర్

M(A)_1=జన
M(A)_2=ఫిబ్ర
M(A)_3=మార్చి
M(A)_4=ఏప్రి
M(A)_5=మే
M(A)_6=జూన్
M(A)_7=జులై
M(A)_8=ఆగ
M(A)_9=సెప్టెం
M(A)_10=అక్టో
M(A)_11=నవం
M(A)_12=డిసెం

M(N)_1=జ
M(N)_2=ఫి
M(N)_3=మా
M(N)_4=ఏ
M(N)_5=మే
M(N)_6=జూ
M(N)_7=జు
M(N)_8=ఆ
M(N)_9=సె
M(N)_10=అ
M(N)_11=న
M(N)_12=డి

M(W)_1=జనవరి
M(W)_2=ఫిబ్రవరి
M(W)_3=మార్చి
M(W)_4=ఏప్రిల్
M(W)_5=మే
M(W)_6=జూన్
M(W)_7=జులై
M(W)_8=ఆగస్టు
M(W)_9=సెప్టెంబర్
M(W)_10=అక్టోబర్
M(W)_11=నవంబర్
M(W)_12=డిసెంబర్

# weekdays
D(a)_1=సోమ
D(a)_2=మంగళ
D(a)_3=బుధ
D(a)_4=గురు
D(a)_5=శుక్ర
D(a)_6=శని
D(a)_7=ఆది

D(n)_1=సో
D(n)_2=మ
D(n)_3=బు
D(n)_4=గు
D(n)_5=శు
D(n)_6=శ
D(n)_7=ఆ

D(s)_1=సోమ
D(s)_2=మం
D(s)_3=బుధ
D(s)_4=గురు
D(s)_5=శుక్ర
D(s)_6=శని
D(s)_7=ఆది

D(w)_1=సోమవారం
D(w)_2=మంగళవారం
D(w)_3=బుధవారం
D(w)_4=గురువారం
D(w)_5=శుక్రవారం
D(w)_6=శనివారం
D(w)_7=ఆదివారం

D(A)_1=సోమ
D(A)_2=మంగళ
D(A)_3=బుధ
D(A)_4=గురు
D(A)_5=శుక్ర
D(A)_6=శని
D(A)_7=ఆది

D(N)_1=సో
D(N)_2=మ
D(N)_3=బు
D(N)_4=గు
D(N)_5=శు
D(N)_6=శ
D(N)_7=ఆ

D(S)_1=సోమ
D(S)_2=మం
D(S)_3=బుధ
D(S)_4=గురు
D(S)_5=శుక్ర
D(S)_6=శని
D(S)_7=ఆది

D(W)_1=సోమవారం
D(W)_2=మంగళవారం
D(W)_3=బుధవారం
D(W)_4=గురువారం
D(W)_5=శుక్రవారం
D(W)_6=శనివారం
D(W)_7=ఆదివారం

# quarters
Q(a)_1=త్రై1
Q(a)_2=త్రై2
Q(a)_3=త్రై3
Q(a)_4=త్రై4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1వ త్రైమాసికం
Q(w)_2=2వ త్రైమాసికం
Q(w)_3=3వ త్రైమాసికం
Q(w)_4=4వ త్రైమాసికం

Q(A)_1=త్రై1
Q(A)_2=త్రై2
Q(A)_3=త్రై3
Q(A)_4=త్రై4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1వ త్రైమాసికం
Q(W)_2=2వ త్రైమాసికం
Q(W)_3=3వ త్రైమాసికం
Q(W)_4=4వ త్రైమాసికం

# day-period-rules
T0600=morning1
T1200=afternoon1
T1800=evening1
T2100=night1

# day-period-translations
P(a)_midnight=అర్ధరాత్రి
P(a)_am=AM
P(a)_pm=PM
P(a)_morning1=ఉదయం
P(a)_afternoon1=మధ్యాహ్నం
P(a)_evening1=సాయంత్రం
P(a)_night1=రాత్రి

P(n)_midnight=అర్ధరాత్రి
P(n)_am=ఉ
P(n)_pm=సా
P(n)_morning1=ఉదయం
P(n)_afternoon1=మధ్యాహ్నం
P(n)_evening1=సాయంత్రం
P(n)_night1=రాత్రి

P(w)_midnight=అర్ధరాత్రి
P(w)_am=AM
P(w)_pm=PM
P(w)_morning1=ఉదయం
P(w)_afternoon1=మధ్యాహ్నం
P(w)_evening1=సాయంత్రం
P(w)_night1=రాత్రి

P(A)_midnight=అర్ధరాత్రి
P(A)_am=AM
P(A)_pm=PM
P(A)_morning1=ఉదయం
P(A)_afternoon1=మధ్యాహ్నం
P(A)_evening1=సాయంత్రం
P(A)_night1=రాత్రి

P(N)_midnight=అర్ధరాత్రి
P(N)_am=AM
P(N)_pm=PM
P(N)_morning1=ఉదయం
P(N)_afternoon1=మధ్యాహ్నం
P(N)_evening1=సాయంత్రం
P(N)_night1=రాత్రి

P(W)_midnight=అర్ధరాత్రి
P(W)_am=AM
P(W)_pm=PM
P(W)_morning1=ఉదయం
P(W)_afternoon1=మధ్యాహ్నం
P(W)_evening1=సాయంత్రం
P(W)_night1=రాత్రి

# eras
E(w)_0=క్రీస్తు పూర్వం
E(w|alt)_0=ప్రస్తుత శకానికి పూర్వం
E(w)_1=క్రీస్తు శకం
E(w|alt)_1=ప్రస్తుత శకం

E(a)_0=క్రీపూ
E(a|alt)_0=BCE
E(a)_1=క్రీశ
E(a|alt)_1=CE

# format patterns
F(f)_d=d, MMMM y, EEEE
F(l)_d=d MMMM, y
F(m)_d=d MMM, y
F(s)_d=dd-MM-yy

F(alt)=h:mm:ss a

F(f)_t=h:mm:ss a zzzz
F(l)_t=h:mm:ss a z
F(m)_t=h:mm:ss a
F(s)_t=h:mm a

F(f)_dt={1} {0}కి
F(l)_dt={1} {0}కి
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d/M
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=M/y
F_yMM=MM-y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw=Yలో wవ వారం

I={0} – {1}

# labels of elements
L_era=యుగం
L_year=సంవత్సరం
L_quarter=త్రైమాసికం
L_month=నెల
L_week=వారము
L_day=దినం
L_weekday=వారంలో రోజు
L_dayperiod=AM/PM
L_hour=గంట
L_minute=నిమిషము
L_second=సెకను
L_zone=సమయ మండలి
