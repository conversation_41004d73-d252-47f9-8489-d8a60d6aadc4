// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Surah _$SurahFromJson(Map<String, dynamic> json) => Surah(
  jozz: (json['jozz'] as num).toInt(),
  name: json['name'] as String,
  page: (json['page'] as num).toInt(),
  verses:
      (json['verses'] as List<dynamic>)
          .map((e) => Verse.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$SurahTo<PERSON>son(Surah instance) => <String, dynamic>{
  'jozz': instance.jozz,
  'name': instance.name,
  'page': instance.page,
  'verses': instance.verses,
};
