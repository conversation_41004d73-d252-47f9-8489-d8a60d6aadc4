import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../providers/quran_provider.dart';
import '../providers/bookmark_provider.dart';
import 'quran_reader_screen.dart';
import 'surah_list_screen.dart';
import 'bookmarks_screen.dart';
import 'settings_screen.dart';
import 'search_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _initializeApp();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// تهيئة التطبيق
  Future<void> _initializeApp() async {
    final settingsProvider = context.read<SettingsProvider>();
    final quranProvider = context.read<QuranProvider>();
    final bookmarkProvider = context.read<BookmarkProvider>();

    // تحميل الإعدادات
    await settingsProvider.loadSettings();
    
    // تحميل بيانات القرآن
    await quranProvider.loadQuranData();
    
    // تحميل العلامات المرجعية
    await bookmarkProvider.loadBookmarks();

    // الانتقال إلى آخر صفحة مقروءة إذا لم يكن هذا التشغيل الأول
    if (!settingsProvider.isFirstLaunch && settingsProvider.lastReadPage > 1) {
      quranProvider.goToPage(settingsProvider.lastReadPage);
    }

    // تعيين أن هذا ليس التشغيل الأول
    if (settingsProvider.isFirstLaunch) {
      await settingsProvider.setIsFirstLaunch(false);
    }
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        children: const [
          QuranReaderScreen(),
          SurahListScreen(),
          BookmarksScreen(),
          SettingsScreen(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: _onTabTapped,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.menu_book),
            label: 'القراءة',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.list),
            label: 'السور',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.bookmark),
            label: 'العلامات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'الإعدادات',
          ),
        ],
      ),
      floatingActionButton: _currentIndex == 0 || _currentIndex == 1
          ? FloatingActionButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SearchScreen(),
                  ),
                );
              },
              child: const Icon(Icons.search),
            )
          : null,
    );
  }
}

/// شاشة الترحيب للمستخدمين الجدد
class WelcomeScreen extends StatelessWidget {
  final VoidCallback onGetStarted;

  const WelcomeScreen({
    super.key,
    required this.onGetStarted,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة التطبيق
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: theme.primaryColor,
                  borderRadius: BorderRadius.circular(60),
                ),
                child: const Icon(
                  Icons.menu_book,
                  size: 60,
                  color: Colors.white,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // عنوان التطبيق
              Text(
                'القرآن الكريم',
                style: theme.textTheme.displayMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.primaryColor,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 8),
              
              // العنوان الفرعي
              Text(
                'برواية قالون عن نافع',
                style: theme.textTheme.titleLarge?.copyWith(
                  color: theme.textTheme.bodyMedium?.color,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 48),
              
              // وصف التطبيق
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      _buildFeatureItem(
                        context,
                        Icons.menu_book,
                        'قراءة القرآن الكريم',
                        'نص كامل برواية قالون مع التشكيل الصحيح',
                      ),
                      const SizedBox(height: 16),
                      _buildFeatureItem(
                        context,
                        Icons.search,
                        'البحث في القرآن',
                        'ابحث في آيات القرآن الكريم بسهولة',
                      ),
                      const SizedBox(height: 16),
                      _buildFeatureItem(
                        context,
                        Icons.bookmark,
                        'العلامات المرجعية',
                        'احفظ آياتك المفضلة للرجوع إليها لاحقاً',
                      ),
                      const SizedBox(height: 16),
                      _buildFeatureItem(
                        context,
                        Icons.settings,
                        'إعدادات متقدمة',
                        'خصص تجربة القراءة حسب تفضيلاتك',
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 48),
              
              // زر البدء
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: onGetStarted,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text(
                    'ابدأ القراءة',
                    style: TextStyle(fontSize: 18),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem(
    BuildContext context,
    IconData icon,
    String title,
    String description,
  ) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: theme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(24),
          ),
          child: Icon(
            icon,
            color: theme.primaryColor,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: theme.textTheme.bodySmall,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
